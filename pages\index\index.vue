<template>
	<view class="container">
		<!-- 顶部导航 -->
		<view class="header">
			<view class="header-left">
				<text class="title">租房管理后台</text>
			</view>
			<view class="header-right">
				<text class="welcome">欢迎，{{ adminInfo.nickname || adminInfo.username }}</text>
				<button class="logout-btn" @click="handleLogout">退出</button>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 侧边栏 -->
			<view class="sidebar">
				<view class="menu-list">
					<view
						class="menu-item"
						:class="{ 'active': currentPage === 'dashboard' }"
						@click="navigateTo('dashboard')"
					>
						<text class="menu-icon">📊</text>
						<text class="menu-text">数据统计</text>
					</view>
					<view
						class="menu-item"
						:class="{ 'active': currentPage === 'house' }"
						@click="navigateTo('house')"
					>
						<text class="menu-icon">🏠</text>
						<text class="menu-text">房源管理</text>
					</view>
					<view
						class="menu-item"
						:class="{ 'active': currentPage === 'user' }"
						@click="navigateTo('user')"
					>
						<text class="menu-icon">👥</text>
						<text class="menu-text">用户管理</text>
					</view>
				</view>
			</view>

			<!-- 内容区域 -->
			<view class="content-area">
				<!-- 数据统计页面 -->
				<view v-if="currentPage === 'dashboard'" class="page-content">
					<view class="page-title">数据统计</view>

					<!-- 统计卡片 -->
					<view class="stats-grid">
						<view class="stat-card">
							<view class="stat-icon">🏠</view>
							<view class="stat-info">
								<text class="stat-number">{{ stats.houseStats.total || 0 }}</text>
								<text class="stat-label">总房源数</text>
							</view>
						</view>
						<view class="stat-card">
							<view class="stat-icon">⏳</view>
							<view class="stat-info">
								<text class="stat-number">{{ stats.houseStats.pending || 0 }}</text>
								<text class="stat-label">待审核</text>
							</view>
						</view>
						<view class="stat-card">
							<view class="stat-icon">✅</view>
							<view class="stat-info">
								<text class="stat-number">{{ stats.houseStats.approved || 0 }}</text>
								<text class="stat-label">已通过</text>
							</view>
						</view>
						<view class="stat-card">
							<view class="stat-icon">👥</view>
							<view class="stat-info">
								<text class="stat-number">{{ stats.userStats.total || 0 }}</text>
								<text class="stat-label">总用户数</text>
							</view>
						</view>
					</view>

					<!-- 今日数据 -->
					<view class="today-stats">
						<view class="section-title">今日数据</view>
						<view class="today-grid">
							<view class="today-item">
								<text class="today-number">{{ stats.todayStats.newHouses || 0 }}</text>
								<text class="today-label">新增房源</text>
							</view>
							<view class="today-item">
								<text class="today-number">{{ stats.todayStats.newUsers || 0 }}</text>
								<text class="today-label">新增用户</text>
							</view>
						</view>
						<button class="detail-btn" @click="navigateTo('dashboard-detail')">查看详细统计</button>
					</view>
				</view>

				<!-- 房源管理页面 -->
				<view v-else-if="currentPage === 'house'" class="page-content">
					<view class="page-title">房源管理</view>
					<view class="loading-text" v-if="loading">加载中...</view>
					<view v-else>
						<text>房源管理功能开发中...</text>
						<button @click="navigateTo('house-list')" class="nav-btn">进入房源列表</button>
					</view>
				</view>

				<!-- 用户管理页面 -->
				<view v-else-if="currentPage === 'user'" class="page-content">
					<view class="page-title">用户管理</view>
					<view class="loading-text" v-if="loading">加载中...</view>
					<view v-else>
						<text>用户管理功能开发中...</text>
						<button @click="navigateTo('user-list')" class="nav-btn">进入用户列表</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentPage: 'dashboard',
			adminInfo: {},
			loading: false,
			stats: {
				houseStats: {},
				userStats: {},
				todayStats: {}
			}
		}
	},

	onLoad() {
		this.checkAuth();
		this.loadStats();
	},

	methods: {
		// 检查登录状态
		checkAuth() {
			const token = uni.getStorageSync('admin_token');
			const adminInfo = uni.getStorageSync('admin_info');

			if (!token || !adminInfo) {
				uni.reLaunch({
					url: '/pages/login/login'
				});
				return;
			}

			this.adminInfo = adminInfo;
		},

		// 加载统计数据
		async loadStats() {
			this.loading = true;
			try {
				const token = uni.getStorageSync('admin_token');

				// 获取房源统计
				const houseStatsResult = await uniCloud.callFunction({
					name: 'admin-house',
					data: {
						action: 'getHouseStats',
						data: { token }
					}
				});

				if (houseStatsResult.result.code === 0) {
					this.stats.houseStats = houseStatsResult.result.data;
				}

				// 模拟用户统计数据（后续可以添加相应的云函数）
				this.stats.userStats = {
					total: 156,
					todayNew: 8,
					banned: 2
				};

				this.stats.todayStats = {
					newHouses: 12,
					newUsers: 8,
					reports: 3
				};

			} catch (error) {
				console.error('加载统计数据失败:', error);
				uni.showToast({
					title: '加载数据失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 页面导航
		navigateTo(page) {
			if (page === 'house-list') {
				uni.navigateTo({
					url: '/pages/house/list'
				});
				return;
			}

			if (page === 'user-list') {
				uni.navigateTo({
					url: '/pages/user/list'
				});
				return;
			}

			if (page === 'dashboard-detail') {
				uni.navigateTo({
					url: '/pages/dashboard/dashboard'
				});
				return;
			}

			this.currentPage = page;
		},

		// 退出登录
		handleLogout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						uni.removeStorageSync('admin_token');
						uni.removeStorageSync('admin_info');
						uni.reLaunch({
							url: '/pages/login/login'
						});
					}
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f7fa;
}

.header {
	height: 120rpx;
	background: #409EFF;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 40rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.header-left .title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
}

.header-right {
	display: flex;
	align-items: center;
}

.welcome {
	font-size: 28rpx;
	color: white;
	margin-right: 20rpx;
}

.logout-btn {
	background: rgba(255, 255, 255, 0.2);
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 24rpx;
	font-size: 24rpx;
}

.main-content {
	display: flex;
	min-height: calc(100vh - 120rpx);
}

.sidebar {
	width: 240rpx;
	background: white;
	box-shadow: 2rpx 0 8rpx rgba(0, 0, 0, 0.1);
}

.menu-list {
	padding: 20rpx 0;
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 24rpx 30rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.menu-item:hover {
	background: #f0f9ff;
}

.menu-item.active {
	background: #e6f7ff;
	border-right: 4rpx solid #409EFF;
}

.menu-icon {
	font-size: 32rpx;
	margin-right: 16rpx;
}

.menu-text {
	font-size: 28rpx;
	color: #333;
}

.menu-item.active .menu-text {
	color: #409EFF;
	font-weight: bold;
}

.content-area {
	flex: 1;
	padding: 40rpx;
	overflow-y: auto;
}

.page-content {
	background: white;
	border-radius: 12rpx;
	padding: 40rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.page-title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 40rpx;
	border-bottom: 2rpx solid #eee;
	padding-bottom: 20rpx;
}

.stats-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
	gap: 30rpx;
	margin-bottom: 40rpx;
}

.stat-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 12rpx;
	padding: 30rpx;
	color: white;
	display: flex;
	align-items: center;
}

.stat-card:nth-child(2) {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card:nth-child(3) {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(4) {
	background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon {
	font-size: 48rpx;
	margin-right: 20rpx;
}

.stat-info {
	display: flex;
	flex-direction: column;
}

.stat-number {
	font-size: 48rpx;
	font-weight: bold;
	line-height: 1;
}

.stat-label {
	font-size: 24rpx;
	opacity: 0.9;
	margin-top: 8rpx;
}

.today-stats {
	margin-top: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.today-grid {
	display: flex;
	gap: 30rpx;
}

.today-item {
	flex: 1;
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 30rpx;
	text-align: center;
	border: 2rpx solid #e9ecef;
}

.today-number {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #409EFF;
	margin-bottom: 8rpx;
}

.today-label {
	font-size: 28rpx;
	color: #666;
}

.loading-text {
	text-align: center;
	color: #666;
	font-size: 28rpx;
	padding: 60rpx 0;
}

.nav-btn {
	background: #409EFF;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
	margin-top: 20rpx;
}

.detail-btn {
	background: #409EFF;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 16rpx 32rpx;
	font-size: 26rpx;
	margin-top: 20rpx;
	width: 100%;
}

/* 响应式设计 */
@media screen and (max-width: 750px) {
	.main-content {
		flex-direction: column;
	}

	.sidebar {
		width: 100%;
		height: auto;
	}

	.menu-list {
		display: flex;
		overflow-x: auto;
		padding: 10rpx 0;
	}

	.menu-item {
		flex-shrink: 0;
		min-width: 120rpx;
		flex-direction: column;
		padding: 20rpx;
	}

	.menu-icon {
		margin-right: 0;
		margin-bottom: 8rpx;
	}

	.stats-grid {
		grid-template-columns: repeat(2, 1fr);
	}

	.today-grid {
		flex-direction: column;
	}
}
</style>
