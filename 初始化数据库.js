/**
 * 数据库初始化脚本
 * 用于创建默认管理员账号和基础数据
 */

const crypto = require('crypto');

// 密码加密函数（与admin-auth云函数保持一致）
function hashPassword(password) {
  return crypto.createHash('sha256').update(password + 'rental_platform_salt').digest('hex');
}

// 初始化管理员账号
async function initAdmin() {
  const db = uniCloud.database();
  
  // 检查是否已存在管理员
  const existAdmin = await db.collection('admin').where({
    role: 'super_admin'
  }).get();
  
  if (existAdmin.data.length > 0) {
    console.log('超级管理员已存在，跳过创建');
    return;
  }
  
  // 创建默认超级管理员
  const adminData = {
    username: 'admin',
    password: hashPassword('123456'), // 默认密码：123456
    nickname: '超级管理员',
    role: 'super_admin',
    created_at: new Date()
  };
  
  const result = await db.collection('admin').add(adminData);
  console.log('超级管理员创建成功，ID:', result.id);
  console.log('默认账号: admin');
  console.log('默认密码: 123456');
  console.log('请及时修改默认密码！');
}

// 初始化示例房源数据
async function initSampleHouses() {
  const db = uniCloud.database();
  
  // 检查是否已有房源数据
  const existHouses = await db.collection('house').count();
  
  if (existHouses.total > 0) {
    console.log('已存在房源数据，跳过创建示例数据');
    return;
  }
  
  // 创建示例用户
  const sampleUser = {
    openid: 'sample_openid_001',
    nickname: '示例用户',
    avatar: 'https://example.com/avatar.jpg',
    phone: '13800138000',
    is_banned: false,
    created_at: new Date(),
    last_login: new Date()
  };
  
  const userResult = await db.collection('user').add(sampleUser);
  const userId = userResult.id;
  
  // 创建示例房源
  const sampleHouses = [
    {
      title: '精装两室一厅，拎包入住',
      desc: '房屋位于市中心，交通便利，周边配套设施齐全。房间干净整洁，家具家电齐全，适合年轻人居住。',
      images: [
        'https://example.com/house1_1.jpg',
        'https://example.com/house1_2.jpg',
        'https://example.com/house1_3.jpg'
      ],
      location: {
        address: '北京市朝阳区建国路88号',
        latitude: 39.9042,
        longitude: 116.4074,
        district: '朝阳区'
      },
      price: 4500,
      type: '两室一厅',
      area: 85,
      floor: '15/25层',
      config: ['空调', '洗衣机', '热水器', '冰箱', '电视', '沙发', '床', '衣柜', '宽带'],
      contact: {
        phone: '13800138000',
        wechat: 'sample_wechat'
      },
      owner_id: userId,
      status: 'approved',
      is_featured: true,
      view_count: 156,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      title: '温馨一室一厅，近地铁站',
      desc: '房屋紧邻地铁站，出行方便。房间温馨舒适，采光良好，适合单身人士或情侣居住。',
      images: [
        'https://example.com/house2_1.jpg',
        'https://example.com/house2_2.jpg'
      ],
      location: {
        address: '北京市海淀区中关村大街123号',
        latitude: 39.9889,
        longitude: 116.3056,
        district: '海淀区'
      },
      price: 3200,
      type: '一室一厅',
      area: 55,
      floor: '8/18层',
      config: ['空调', '热水器', '冰箱', '床', '衣柜', '宽带'],
      contact: {
        phone: '13800138001',
        wechat: 'sample_wechat2'
      },
      owner_id: userId,
      status: 'approved',
      is_featured: false,
      view_count: 89,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      title: '合租房间，环境优雅',
      desc: '三室一厅中的一间，与两位室友合租。房间独立，公共区域整洁，室友素质高。',
      images: [
        'https://example.com/house3_1.jpg'
      ],
      location: {
        address: '北京市西城区西单北大街456号',
        latitude: 39.9139,
        longitude: 116.3831,
        district: '西城区'
      },
      price: 2800,
      type: '合租',
      area: 20,
      floor: '6/12层',
      config: ['空调', '床', '衣柜', '书桌', '宽带'],
      contact: {
        phone: '13800138002'
      },
      owner_id: userId,
      status: 'pending',
      is_featured: false,
      view_count: 23,
      created_at: new Date(),
      updated_at: new Date()
    }
  ];
  
  for (const house of sampleHouses) {
    await db.collection('house').add(house);
  }
  
  console.log(`示例房源数据创建成功，共创建 ${sampleHouses.length} 条记录`);
}

// 主初始化函数
async function init() {
  try {
    console.log('开始初始化数据库...');
    
    await initAdmin();
    await initSampleHouses();
    
    console.log('数据库初始化完成！');
    
    console.log('\n=== 重要信息 ===');
    console.log('管理员账号: admin');
    console.log('管理员密码: 123456');
    console.log('请立即登录管理后台修改默认密码！');
    console.log('================');
    
  } catch (error) {
    console.error('初始化失败:', error);
  }
}

// 执行初始化
init();
