'use strict';

const crypto = require('crypto');

/**
 * 管理员认证云函数
 * 处理管理员登录、权限验证等功能
 */
exports.main = async (event, context) => {
  const { action, data } = event;

  // 统一返回格式
  const response = {
    code: 0,
    message: '操作成功',
    data: null
  };

  try {
    switch (action) {
      case 'adminLogin':
        return await adminLogin(data);
      case 'checkAdminToken':
        return await checkAdminToken(data);
      case 'changePassword':
        return await changePassword(data);
      case 'createAdmin':
        return await createAdmin(data);
      default:
        response.code = 1001;
        response.message = '未知操作类型';
        return response;
    }
  } catch (error) {
    console.error('管理员认证云函数错误:', error);
    response.code = 2001;
    response.message = '服务器错误';
    return response;
  }
};

/**
 * 管理员登录
 */
async function adminLogin(data) {
  try {
    const { username, password } = data;
    
    if (!username || !password) {
      return {
        code: 1001,
        message: '用户名和密码不能为空'
      };
    }

    const db = uniCloud.database();
    
    // 查找管理员
    const adminResult = await db.collection('admin').where({
      username: username
    }).get();

    if (adminResult.data.length === 0) {
      return {
        code: 1004,
        message: '管理员不存在'
      };
    }

    const admin = adminResult.data[0];
    
    // 验证密码
    const hashedPassword = hashPassword(password);
    
    if (admin.password !== hashedPassword) {
      return {
        code: 1005,
        message: '密码错误'
      };
    }

    // 生成token
    const token = generateToken(admin._id, admin.role);

    return {
      code: 0,
      message: '登录成功',
      data: {
        token: token,
        adminInfo: {
          id: admin._id,
          username: admin.username,
          nickname: admin.nickname,
          role: admin.role
        }
      }
    };

  } catch (error) {
    console.error('管理员登录错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 验证管理员token
 */
async function checkAdminToken(data) {
  try {
    const { token } = data;
    
    if (!token) {
      return {
        code: 1002,
        message: '缺少token'
      };
    }

    // 解析token
    const tokenData = parseToken(token);
    
    if (!tokenData) {
      return {
        code: 1002,
        message: 'token无效'
      };
    }

    // 检查token是否过期
    if (tokenData.exp < Date.now()) {
      return {
        code: 1002,
        message: 'token已过期'
      };
    }

    const db = uniCloud.database();
    
    // 验证管理员是否存在
    const adminResult = await db.collection('admin').doc(tokenData.adminId).get();

    if (adminResult.data.length === 0) {
      return {
        code: 1004,
        message: '管理员不存在'
      };
    }

    const admin = adminResult.data[0];

    return {
      code: 0,
      message: '验证成功',
      data: {
        adminInfo: {
          id: admin._id,
          username: admin.username,
          nickname: admin.nickname,
          role: admin.role
        }
      }
    };

  } catch (error) {
    console.error('验证管理员token错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 修改密码
 */
async function changePassword(data) {
  try {
    const { token, oldPassword, newPassword } = data;
    
    // 验证token
    const tokenCheck = await checkAdminToken({ token });
    
    if (tokenCheck.code !== 0) {
      return tokenCheck;
    }

    if (!oldPassword || !newPassword) {
      return {
        code: 1001,
        message: '旧密码和新密码不能为空'
      };
    }

    if (newPassword.length < 6) {
      return {
        code: 1001,
        message: '新密码长度不能少于6位'
      };
    }

    const db = uniCloud.database();
    const adminId = tokenCheck.data.adminInfo.id;
    
    // 获取管理员信息
    const adminResult = await db.collection('admin').doc(adminId).get();
    const admin = adminResult.data[0];
    
    // 验证旧密码
    const hashedOldPassword = hashPassword(oldPassword);
    
    if (admin.password !== hashedOldPassword) {
      return {
        code: 1005,
        message: '旧密码错误'
      };
    }

    // 更新密码
    const hashedNewPassword = hashPassword(newPassword);
    
    await db.collection('admin').doc(adminId).update({
      password: hashedNewPassword
    });

    return {
      code: 0,
      message: '密码修改成功'
    };

  } catch (error) {
    console.error('修改密码错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 创建管理员（仅超级管理员可用）
 */
async function createAdmin(data) {
  try {
    const { token, username, password, nickname, role = 'admin' } = data;
    
    // 验证token
    const tokenCheck = await checkAdminToken({ token });
    
    if (tokenCheck.code !== 0) {
      return tokenCheck;
    }

    // 检查权限
    if (tokenCheck.data.adminInfo.role !== 'super_admin') {
      return {
        code: 1003,
        message: '权限不足'
      };
    }

    if (!username || !password) {
      return {
        code: 1001,
        message: '用户名和密码不能为空'
      };
    }

    const db = uniCloud.database();
    
    // 检查用户名是否已存在
    const existAdmin = await db.collection('admin').where({
      username: username
    }).get();

    if (existAdmin.data.length > 0) {
      return {
        code: 1005,
        message: '用户名已存在'
      };
    }

    // 创建管理员
    const adminData = {
      username: username,
      password: hashPassword(password),
      nickname: nickname || username,
      role: role,
      created_at: new Date()
    };

    const result = await db.collection('admin').add(adminData);

    return {
      code: 0,
      message: '创建成功',
      data: {
        adminId: result.id
      }
    };

  } catch (error) {
    console.error('创建管理员错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 密码加密
 */
function hashPassword(password) {
  return crypto.createHash('sha256').update(password + 'rental_platform_salt').digest('hex');
}

/**
 * 生成token
 */
function generateToken(adminId, role) {
  const payload = {
    adminId: adminId,
    role: role,
    exp: Date.now() + 24 * 60 * 60 * 1000 // 24小时过期
  };
  
  return Buffer.from(JSON.stringify(payload)).toString('base64');
}

/**
 * 解析token
 */
function parseToken(token) {
  try {
    const payload = JSON.parse(Buffer.from(token, 'base64').toString());
    return payload;
  } catch (error) {
    return null;
  }
}
