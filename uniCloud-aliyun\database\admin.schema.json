{"bsonType": "object", "description": "管理员信息集合", "required": ["username", "password", "role"], "properties": {"_id": {"description": "管理员ID"}, "username": {"bsonType": "string", "description": "管理员账号", "maxLength": 50, "minLength": 3, "title": "管理员账号"}, "password": {"bsonType": "string", "description": "密码（加密）", "title": "密码"}, "role": {"bsonType": "string", "description": "角色", "enum": ["super_admin", "admin"], "defaultValue": "admin", "title": "角色"}, "nickname": {"bsonType": "string", "description": "昵称", "maxLength": 50, "title": "昵称"}, "created_at": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}, "title": "创建时间"}}, "permission": {"read": "'admin' in auth.role", "create": "'super_admin' in auth.role", "update": "'super_admin' in auth.role", "delete": "'super_admin' in auth.role"}, "index": [{"IndexName": "username", "MgoKeySchema": {"username": 1}, "unique": true}]}