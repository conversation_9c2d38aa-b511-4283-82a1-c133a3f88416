<template>
	<view class="container">
		<!-- 顶部操作栏 -->
		<view class="header">
			<view class="header-left">
				<text class="title">房源管理</text>
			</view>
			<view class="header-right">
				<button class="refresh-btn" @click="loadHouseList">刷新</button>
			</view>
		</view>
		
		<!-- 筛选栏 -->
		<view class="filter-bar">
			<view class="filter-item">
				<text class="filter-label">状态：</text>
				<picker 
					:value="filterIndex" 
					:range="statusOptions" 
					range-key="text"
					@change="onStatusChange"
				>
					<view class="picker">{{ statusOptions[filterIndex].text }}</view>
				</picker>
			</view>
			<view class="filter-item">
				<text class="filter-label">搜索：</text>
				<input 
					class="search-input" 
					type="text" 
					placeholder="输入房源标题或地址" 
					v-model="keyword"
					@confirm="handleSearch"
				/>
				<button class="search-btn" @click="handleSearch">搜索</button>
			</view>
		</view>
		
		<!-- 房源列表 -->
		<view class="content">
			<view v-if="loading" class="loading">
				<text>加载中...</text>
			</view>
			
			<view v-else-if="houseList.length === 0" class="empty">
				<text>暂无房源数据</text>
			</view>
			
			<view v-else class="house-list">
				<view 
					v-for="(house, index) in houseList" 
					:key="house._id" 
					class="house-item"
				>
					<!-- 房源信息 -->
					<view class="house-info">
						<view class="house-images" v-if="house.images && house.images.length > 0">
							<image 
								:src="house.images[0]" 
								mode="aspectFill"
								class="house-image"
								@error="onImageError"
							></image>
						</view>
						<view class="house-details">
							<text class="house-title">{{ house.title }}</text>
							<text class="house-location">📍 {{ house.location.address }}</text>
							<text class="house-price">💰 ¥{{ house.price }}/月</text>
							<text class="house-type">🏠 {{ house.type }} · {{ house.area }}㎡</text>
							<text class="house-time">⏰ {{ formatTime(house.created_at) }}</text>
						</view>
					</view>
					
					<!-- 状态标签 -->
					<view class="status-tag" :class="getStatusClass(house.status)">
						{{ getStatusText(house.status) }}
					</view>
					
					<!-- 发布者信息 -->
					<view class="owner-info" v-if="house.owner_info">
						<text class="owner-name">发布者：{{ house.owner_info.nickname || '未知' }}</text>
						<text class="owner-phone" v-if="house.owner_info.phone">{{ house.owner_info.phone }}</text>
					</view>
					
					<!-- 操作按钮 -->
					<view class="actions">
						<button 
							v-if="house.status === 'pending'" 
							class="approve-btn" 
							@click="auditHouse(house._id, 'approved')"
						>
							通过
						</button>
						<button 
							v-if="house.status === 'pending'" 
							class="reject-btn" 
							@click="showRejectModal(house._id)"
						>
							驳回
						</button>
						<button 
							v-if="house.status === 'approved'" 
							class="feature-btn" 
							:class="{ 'featured': house.is_featured }"
							@click="toggleFeatured(house._id, !house.is_featured)"
						>
							{{ house.is_featured ? '取消推荐' : '设为推荐' }}
						</button>
						<button class="delete-btn" @click="deleteHouse(house._id)">删除</button>
					</view>
				</view>
			</view>
			
			<!-- 分页 -->
			<view v-if="houseList.length > 0" class="pagination">
				<button 
					class="page-btn" 
					:disabled="currentPage <= 1"
					@click="changePage(currentPage - 1)"
				>
					上一页
				</button>
				<text class="page-info">{{ currentPage }} / {{ totalPages }}</text>
				<button 
					class="page-btn" 
					:disabled="currentPage >= totalPages"
					@click="changePage(currentPage + 1)"
				>
					下一页
				</button>
			</view>
		</view>
		
		<!-- 驳回原因弹窗 -->
		<view v-if="showRejectDialog" class="modal-overlay" @click="hideRejectModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">驳回原因</text>
					<text class="modal-close" @click="hideRejectModal">×</text>
				</view>
				<view class="modal-body">
					<textarea 
						class="reject-textarea" 
						placeholder="请输入驳回原因..." 
						v-model="rejectReason"
						maxlength="200"
					></textarea>
				</view>
				<view class="modal-footer">
					<button class="cancel-btn" @click="hideRejectModal">取消</button>
					<button class="confirm-btn" @click="confirmReject">确认驳回</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			loading: false,
			houseList: [],
			currentPage: 1,
			pageSize: 10,
			total: 0,
			keyword: '',
			filterIndex: 0,
			statusOptions: [
				{ value: '', text: '全部状态' },
				{ value: 'pending', text: '待审核' },
				{ value: 'approved', text: '已通过' },
				{ value: 'rejected', text: '已驳回' }
			],
			showRejectDialog: false,
			rejectReason: '',
			currentRejectId: ''
		}
	},
	
	computed: {
		totalPages() {
			return Math.ceil(this.total / this.pageSize);
		}
	},
	
	onLoad() {
		this.checkAuth();
		this.loadHouseList();
	},
	
	methods: {
		// 检查登录状态
		checkAuth() {
			const token = uni.getStorageSync('admin_token');
			if (!token) {
				uni.reLaunch({
					url: '/pages/login/login'
				});
			}
		},
		
		// 加载房源列表
		async loadHouseList() {
			this.loading = true;
			try {
				const token = uni.getStorageSync('admin_token');
				const status = this.statusOptions[this.filterIndex].value;
				
				const result = await uniCloud.callFunction({
					name: 'admin-house',
					data: {
						action: 'getHouseList',
						data: {
							token,
							page: this.currentPage,
							limit: this.pageSize,
							status,
							keyword: this.keyword
						}
					}
				});
				
				if (result.result.code === 0) {
					this.houseList = result.result.data.list;
					this.total = result.result.data.total;
				} else {
					uni.showToast({
						title: result.result.message || '加载失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('加载房源列表失败:', error);
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 状态筛选
		onStatusChange(e) {
			this.filterIndex = e.detail.value;
			this.currentPage = 1;
			this.loadHouseList();
		},
		
		// 搜索
		handleSearch() {
			this.currentPage = 1;
			this.loadHouseList();
		},
		
		// 分页
		changePage(page) {
			if (page < 1 || page > this.totalPages) return;
			this.currentPage = page;
			this.loadHouseList();
		},
		
		// 审核房源
		async auditHouse(houseId, status) {
			try {
				const token = uni.getStorageSync('admin_token');
				
				const result = await uniCloud.callFunction({
					name: 'admin-house',
					data: {
						action: 'auditHouse',
						data: {
							token,
							houseId,
							status,
							reason: status === 'rejected' ? this.rejectReason : ''
						}
					}
				});
				
				if (result.result.code === 0) {
					uni.showToast({
						title: status === 'approved' ? '审核通过' : '审核驳回',
						icon: 'success'
					});
					this.loadHouseList();
				} else {
					uni.showToast({
						title: result.result.message || '操作失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('审核失败:', error);
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				});
			}
		},
		
		// 显示驳回弹窗
		showRejectModal(houseId) {
			this.currentRejectId = houseId;
			this.rejectReason = '';
			this.showRejectDialog = true;
		},
		
		// 隐藏驳回弹窗
		hideRejectModal() {
			this.showRejectDialog = false;
			this.currentRejectId = '';
			this.rejectReason = '';
		},
		
		// 确认驳回
		confirmReject() {
			if (!this.rejectReason.trim()) {
				uni.showToast({
					title: '请输入驳回原因',
					icon: 'none'
				});
				return;
			}
			
			this.auditHouse(this.currentRejectId, 'rejected');
			this.hideRejectModal();
		},
		
		// 切换推荐状态
		async toggleFeatured(houseId, isFeatured) {
			try {
				const token = uni.getStorageSync('admin_token');
				
				const result = await uniCloud.callFunction({
					name: 'admin-house',
					data: {
						action: 'setFeatured',
						data: {
							token,
							houseId,
							isFeatured
						}
					}
				});
				
				if (result.result.code === 0) {
					uni.showToast({
						title: isFeatured ? '设为推荐成功' : '取消推荐成功',
						icon: 'success'
					});
					this.loadHouseList();
				} else {
					uni.showToast({
						title: result.result.message || '操作失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('设置推荐失败:', error);
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				});
			}
		},
		
		// 删除房源
		deleteHouse(houseId) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个房源吗？此操作不可恢复。',
				success: async (res) => {
					if (res.confirm) {
						try {
							const token = uni.getStorageSync('admin_token');
							
							const result = await uniCloud.callFunction({
								name: 'admin-house',
								data: {
									action: 'deleteHouse',
									data: {
										token,
										houseId
									}
								}
							});
							
							if (result.result.code === 0) {
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								});
								this.loadHouseList();
							} else {
								uni.showToast({
									title: result.result.message || '删除失败',
									icon: 'none'
								});
							}
						} catch (error) {
							console.error('删除失败:', error);
							uni.showToast({
								title: '网络错误',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		
		// 获取状态样式类
		getStatusClass(status) {
			const classMap = {
				'pending': 'status-pending',
				'approved': 'status-approved',
				'rejected': 'status-rejected'
			};
			return classMap[status] || '';
		},
		
		// 获取状态文本
		getStatusText(status) {
			const textMap = {
				'pending': '待审核',
				'approved': '已通过',
				'rejected': '已驳回'
			};
			return textMap[status] || '未知';
		},
		
		// 格式化时间
		formatTime(timestamp) {
			if (!timestamp) return '';
			const date = new Date(timestamp);
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
		},
		
		// 图片加载失败处理
		onImageError(e) {
			console.log('图片加载失败:', e);
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f7fa;
}

.header {
	height: 120rpx;
	background: white;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 40rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.refresh-btn {
	background: #409EFF;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
}

.filter-bar {
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	align-items: center;
	gap: 40rpx;
	border-bottom: 2rpx solid #eee;
}

.filter-item {
	display: flex;
	align-items: center;
}

.filter-label {
	font-size: 28rpx;
	color: #666;
	margin-right: 16rpx;
}

.picker {
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
	border-radius: 8rpx;
	padding: 16rpx 24rpx;
	font-size: 28rpx;
	min-width: 160rpx;
	text-align: center;
}

.search-input {
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
	border-radius: 8rpx;
	padding: 16rpx 24rpx;
	font-size: 28rpx;
	width: 400rpx;
	margin-right: 16rpx;
}

.search-btn {
	background: #409EFF;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
}

.content {
	padding: 40rpx;
}

.loading, .empty {
	text-align: center;
	padding: 120rpx 0;
	color: #666;
	font-size: 28rpx;
}

.house-list {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.house-item {
	background: white;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
	position: relative;
}

.house-info {
	display: flex;
	margin-bottom: 20rpx;
}

.house-images {
	width: 200rpx;
	height: 150rpx;
	margin-right: 30rpx;
	flex-shrink: 0;
}

.house-image {
	width: 100%;
	height: 100%;
	border-radius: 8rpx;
}

.house-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.house-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.4;
}

.house-location,
.house-price,
.house-type,
.house-time {
	font-size: 26rpx;
	color: #666;
}

.house-price {
	color: #e74c3c;
	font-weight: bold;
}

.status-tag {
	position: absolute;
	top: 30rpx;
	right: 30rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: white;
}

.status-pending {
	background: #f39c12;
}

.status-approved {
	background: #27ae60;
}

.status-rejected {
	background: #e74c3c;
}

.owner-info {
	margin: 20rpx 0;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.owner-name,
.owner-phone {
	font-size: 26rpx;
	color: #666;
}

.actions {
	display: flex;
	gap: 20rpx;
	margin-top: 20rpx;
}

.approve-btn,
.reject-btn,
.feature-btn,
.delete-btn {
	padding: 16rpx 32rpx;
	border: none;
	border-radius: 8rpx;
	font-size: 26rpx;
	cursor: pointer;
}

.approve-btn {
	background: #27ae60;
	color: white;
}

.reject-btn {
	background: #e74c3c;
	color: white;
}

.feature-btn {
	background: #f39c12;
	color: white;
}

.feature-btn.featured {
	background: #95a5a6;
}

.delete-btn {
	background: #e74c3c;
	color: white;
}

.pagination {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 30rpx;
	margin-top: 40rpx;
	padding: 40rpx 0;
}

.page-btn {
	background: #409EFF;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
}

.page-btn:disabled {
	background: #ccc;
	cursor: not-allowed;
}

.page-info {
	font-size: 28rpx;
	color: #666;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	background: white;
	border-radius: 12rpx;
	width: 80%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 2rpx solid #eee;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	font-size: 48rpx;
	color: #999;
	cursor: pointer;
}

.modal-body {
	padding: 40rpx;
}

.reject-textarea {
	width: 100%;
	min-height: 200rpx;
	border: 2rpx solid #e9ecef;
	border-radius: 8rpx;
	padding: 20rpx;
	font-size: 28rpx;
	resize: none;
}

.modal-footer {
	display: flex;
	justify-content: flex-end;
	gap: 20rpx;
	padding: 30rpx 40rpx;
	border-top: 2rpx solid #eee;
}

.cancel-btn,
.confirm-btn {
	padding: 16rpx 32rpx;
	border: none;
	border-radius: 8rpx;
	font-size: 28rpx;
}

.cancel-btn {
	background: #95a5a6;
	color: white;
}

.confirm-btn {
	background: #e74c3c;
	color: white;
}

/* 响应式设计 */
@media screen and (max-width: 750px) {
	.filter-bar {
		flex-direction: column;
		gap: 20rpx;
		align-items: stretch;
	}

	.filter-item {
		justify-content: space-between;
	}

	.search-input {
		width: 100%;
		margin-right: 0;
		margin-bottom: 16rpx;
	}

	.house-info {
		flex-direction: column;
	}

	.house-images {
		width: 100%;
		height: 300rpx;
		margin-right: 0;
		margin-bottom: 20rpx;
	}

	.actions {
		flex-wrap: wrap;
	}

	.modal-content {
		width: 95%;
	}
}
</style>
