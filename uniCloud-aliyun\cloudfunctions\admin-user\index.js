'use strict';

const db = uniCloud.database();
const dbCmd = db.command;

exports.main = async (event, context) => {
	const { action, data } = event;
	
	// 验证管理员token
	const tokenResult = await verifyAdminToken(data.token);
	if (tokenResult.code !== 0) {
		return tokenResult;
	}
	
	try {
		switch (action) {
			case 'getUserList':
				return await getUserList(data);
			case 'banUser':
				return await banUser(data);
			case 'unbanUser':
				return await unbanUser(data);
			case 'getUserStats':
				return await getUserStats(data);
			default:
				return {
					code: 400,
					message: '无效的操作类型'
				};
		}
	} catch (error) {
		console.error('admin-user 云函数执行错误:', error);
		return {
			code: 500,
			message: '服务器内部错误',
			error: error.message
		};
	}
};

// 验证管理员token
async function verifyAdminToken(token) {
	if (!token) {
		return {
			code: 401,
			message: '缺少token'
		};
	}
	
	try {
		// 这里应该验证token的有效性
		// 简化处理，实际应该解析JWT token
		const adminCollection = db.collection('admin');
		const adminResult = await adminCollection.where({
			token: token
		}).get();
		
		if (adminResult.data.length === 0) {
			return {
				code: 401,
				message: 'token无效'
			};
		}
		
		return {
			code: 0,
			data: adminResult.data[0]
		};
	} catch (error) {
		return {
			code: 500,
			message: '验证token失败'
		};
	}
}

// 获取用户列表
async function getUserList(data) {
	const { page = 1, limit = 10, status = '', keyword = '' } = data;
	const skip = (page - 1) * limit;
	
	try {
		const userCollection = db.collection('user');
		let whereCondition = {};
		
		// 状态筛选
		if (status === 'normal') {
			whereCondition.is_banned = false;
		} else if (status === 'banned') {
			whereCondition.is_banned = true;
		}
		
		// 关键词搜索
		if (keyword) {
			whereCondition = dbCmd.and([
				whereCondition,
				dbCmd.or([
					{ nickname: new RegExp(keyword, 'i') },
					{ phone: new RegExp(keyword, 'i') }
				])
			]);
		}
		
		// 获取总数
		const countResult = await userCollection.where(whereCondition).count();
		const total = countResult.total;
		
		// 获取用户列表
		const listResult = await userCollection
			.where(whereCondition)
			.orderBy('created_at', 'desc')
			.skip(skip)
			.limit(limit)
			.get();
		
		// 为每个用户添加统计信息
		const userList = await Promise.all(listResult.data.map(async (user) => {
			// 获取用户发布的房源数量
			const houseCountResult = await db.collection('house')
				.where({ owner_id: user._id })
				.count();
			
			// 获取用户收藏数量
			const favoriteCountResult = await db.collection('favorites')
				.where({ user_id: user._id })
				.count();
			
			return {
				...user,
				house_count: houseCountResult.total,
				favorite_count: favoriteCountResult.total
			};
		}));
		
		return {
			code: 0,
			message: '获取成功',
			data: {
				list: userList,
				total,
				page,
				limit
			}
		};
	} catch (error) {
		console.error('获取用户列表失败:', error);
		return {
			code: 500,
			message: '获取用户列表失败'
		};
	}
}

// 封禁用户
async function banUser(data) {
	const { userId, reason } = data;
	
	if (!userId) {
		return {
			code: 400,
			message: '缺少用户ID'
		};
	}
	
	if (!reason || !reason.trim()) {
		return {
			code: 400,
			message: '缺少封禁原因'
		};
	}
	
	try {
		const userCollection = db.collection('user');
		
		// 检查用户是否存在
		const userResult = await userCollection.doc(userId).get();
		if (userResult.data.length === 0) {
			return {
				code: 404,
				message: '用户不存在'
			};
		}
		
		// 更新用户状态
		await userCollection.doc(userId).update({
			is_banned: true,
			ban_reason: reason.trim(),
			ban_time: Date.now()
		});
		
		return {
			code: 0,
			message: '封禁成功'
		};
	} catch (error) {
		console.error('封禁用户失败:', error);
		return {
			code: 500,
			message: '封禁用户失败'
		};
	}
}

// 解除封禁
async function unbanUser(data) {
	const { userId } = data;
	
	if (!userId) {
		return {
			code: 400,
			message: '缺少用户ID'
		};
	}
	
	try {
		const userCollection = db.collection('user');
		
		// 检查用户是否存在
		const userResult = await userCollection.doc(userId).get();
		if (userResult.data.length === 0) {
			return {
				code: 404,
				message: '用户不存在'
			};
		}
		
		// 更新用户状态
		await userCollection.doc(userId).update({
			is_banned: false,
			ban_reason: '',
			unban_time: Date.now()
		});
		
		return {
			code: 0,
			message: '解封成功'
		};
	} catch (error) {
		console.error('解封用户失败:', error);
		return {
			code: 500,
			message: '解封用户失败'
		};
	}
}

// 获取用户统计
async function getUserStats(data) {
	try {
		const userCollection = db.collection('user');
		
		// 总用户数
		const totalResult = await userCollection.count();
		
		// 正常用户数
		const normalResult = await userCollection.where({
			is_banned: false
		}).count();
		
		// 封禁用户数
		const bannedResult = await userCollection.where({
			is_banned: true
		}).count();
		
		// 今日新增用户数
		const today = new Date();
		today.setHours(0, 0, 0, 0);
		const todayTimestamp = today.getTime();
		
		const todayNewResult = await userCollection.where({
			created_at: dbCmd.gte(todayTimestamp)
		}).count();
		
		return {
			code: 0,
			message: '获取成功',
			data: {
				total: totalResult.total,
				normal: normalResult.total,
				banned: bannedResult.total,
				todayNew: todayNewResult.total
			}
		};
	} catch (error) {
		console.error('获取用户统计失败:', error);
		return {
			code: 500,
			message: '获取用户统计失败'
		};
	}
}
