<template>
	<view class="container">
		<!-- 顶部操作栏 -->
		<view class="header">
			<view class="header-left">
				<text class="title">数据统计</text>
			</view>
			<view class="header-right">
				<button class="refresh-btn" @click="loadAllStats">刷新数据</button>
			</view>
		</view>
		
		<!-- 主要统计卡片 -->
		<view class="content">
			<view v-if="loading" class="loading">
				<text>加载中...</text>
			</view>
			
			<view v-else class="stats-container">
				<!-- 总览统计 -->
				<view class="section">
					<view class="section-title">总览统计</view>
					<view class="stats-grid">
						<view class="stat-card total">
							<view class="stat-icon">🏠</view>
							<view class="stat-info">
								<text class="stat-number">{{ houseStats.total || 0 }}</text>
								<text class="stat-label">总房源数</text>
							</view>
						</view>
						<view class="stat-card pending">
							<view class="stat-icon">⏳</view>
							<view class="stat-info">
								<text class="stat-number">{{ houseStats.pending || 0 }}</text>
								<text class="stat-label">待审核</text>
							</view>
						</view>
						<view class="stat-card approved">
							<view class="stat-icon">✅</view>
							<view class="stat-info">
								<text class="stat-number">{{ houseStats.approved || 0 }}</text>
								<text class="stat-label">已通过</text>
							</view>
						</view>
						<view class="stat-card users">
							<view class="stat-icon">👥</view>
							<view class="stat-info">
								<text class="stat-number">{{ userStats.total || 0 }}</text>
								<text class="stat-label">总用户数</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 今日数据 -->
				<view class="section">
					<view class="section-title">今日数据</view>
					<view class="today-stats">
						<view class="today-item">
							<view class="today-icon">🆕</view>
							<view class="today-info">
								<text class="today-number">{{ todayStats.newHouses || 0 }}</text>
								<text class="today-label">新增房源</text>
							</view>
						</view>
						<view class="today-item">
							<view class="today-icon">👤</view>
							<view class="today-info">
								<text class="today-number">{{ todayStats.newUsers || 0 }}</text>
								<text class="today-label">新增用户</text>
							</view>
						</view>
						<view class="today-item">
							<view class="today-icon">📝</view>
							<view class="today-info">
								<text class="today-number">{{ todayStats.pendingAudit || 0 }}</text>
								<text class="today-label">待审核</text>
							</view>
						</view>
						<view class="today-item">
							<view class="today-icon">🚨</view>
							<view class="today-info">
								<text class="today-number">{{ todayStats.reports || 0 }}</text>
								<text class="today-label">举报数</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 房源状态分布 -->
				<view class="section">
					<view class="section-title">房源状态分布</view>
					<view class="chart-container">
						<view class="chart-item">
							<view class="chart-bar">
								<view 
									class="bar-fill approved-bar" 
									:style="{ width: getPercentage(houseStats.approved, houseStats.total) + '%' }"
								></view>
							</view>
							<view class="chart-label">
								<text class="label-text">已通过</text>
								<text class="label-value">{{ houseStats.approved || 0 }}</text>
							</view>
						</view>
						<view class="chart-item">
							<view class="chart-bar">
								<view 
									class="bar-fill pending-bar" 
									:style="{ width: getPercentage(houseStats.pending, houseStats.total) + '%' }"
								></view>
							</view>
							<view class="chart-label">
								<text class="label-text">待审核</text>
								<text class="label-value">{{ houseStats.pending || 0 }}</text>
							</view>
						</view>
						<view class="chart-item">
							<view class="chart-bar">
								<view 
									class="bar-fill rejected-bar" 
									:style="{ width: getPercentage(houseStats.rejected, houseStats.total) + '%' }"
								></view>
							</view>
							<view class="chart-label">
								<text class="label-text">已驳回</text>
								<text class="label-value">{{ houseStats.rejected || 0 }}</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 用户状态统计 -->
				<view class="section">
					<view class="section-title">用户状态统计</view>
					<view class="user-stats-grid">
						<view class="user-stat-item normal">
							<view class="user-stat-icon">😊</view>
							<view class="user-stat-info">
								<text class="user-stat-number">{{ userStats.normal || 0 }}</text>
								<text class="user-stat-label">正常用户</text>
							</view>
						</view>
						<view class="user-stat-item banned">
							<view class="user-stat-icon">🚫</view>
							<view class="user-stat-info">
								<text class="user-stat-number">{{ userStats.banned || 0 }}</text>
								<text class="user-stat-label">封禁用户</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 快捷操作 -->
				<view class="section">
					<view class="section-title">快捷操作</view>
					<view class="quick-actions">
						<button class="action-btn house-btn" @click="goToHouseManage">
							<text class="action-icon">🏠</text>
							<text class="action-text">房源管理</text>
						</button>
						<button class="action-btn user-btn" @click="goToUserManage">
							<text class="action-icon">👥</text>
							<text class="action-text">用户管理</text>
						</button>
						<button class="action-btn audit-btn" @click="goToAudit">
							<text class="action-icon">📝</text>
							<text class="action-text">待审核房源</text>
						</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			loading: false,
			houseStats: {},
			userStats: {},
			todayStats: {}
		}
	},
	
	onLoad() {
		this.checkAuth();
		this.loadAllStats();
	},
	
	methods: {
		// 检查登录状态
		checkAuth() {
			const token = uni.getStorageSync('admin_token');
			if (!token) {
				uni.reLaunch({
					url: '/pages/login/login'
				});
			}
		},
		
		// 加载所有统计数据
		async loadAllStats() {
			this.loading = true;
			try {
				await Promise.all([
					this.loadHouseStats(),
					this.loadUserStats(),
					this.loadTodayStats()
				]);
			} catch (error) {
				console.error('加载统计数据失败:', error);
				uni.showToast({
					title: '加载数据失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 加载房源统计
		async loadHouseStats() {
			try {
				const token = uni.getStorageSync('admin_token');
				
				const result = await uniCloud.callFunction({
					name: 'admin-house',
					data: {
						action: 'getHouseStats',
						data: { token }
					}
				});
				
				if (result.result.code === 0) {
					this.houseStats = result.result.data;
				}
			} catch (error) {
				console.error('加载房源统计失败:', error);
				// 模拟数据
				this.houseStats = {
					total: 156,
					pending: 23,
					approved: 128,
					rejected: 5
				};
			}
		},
		
		// 加载用户统计
		async loadUserStats() {
			try {
				const token = uni.getStorageSync('admin_token');
				
				const result = await uniCloud.callFunction({
					name: 'admin-user',
					data: {
						action: 'getUserStats',
						data: { token }
					}
				});
				
				if (result.result.code === 0) {
					this.userStats = result.result.data;
				}
			} catch (error) {
				console.error('加载用户统计失败:', error);
				// 模拟数据
				this.userStats = {
					total: 89,
					normal: 85,
					banned: 4
				};
			}
		},
		
		// 加载今日统计
		async loadTodayStats() {
			try {
				// 这里可以调用相应的云函数获取今日数据
				// 暂时使用模拟数据
				this.todayStats = {
					newHouses: 12,
					newUsers: 8,
					pendingAudit: 5,
					reports: 2
				};
			} catch (error) {
				console.error('加载今日统计失败:', error);
			}
		},
		
		// 计算百分比
		getPercentage(value, total) {
			if (!total || total === 0) return 0;
			return Math.round((value / total) * 100);
		},
		
		// 跳转到房源管理
		goToHouseManage() {
			uni.navigateTo({
				url: '/pages/house/list'
			});
		},
		
		// 跳转到用户管理
		goToUserManage() {
			uni.navigateTo({
				url: '/pages/user/list'
			});
		},
		
		// 跳转到待审核房源
		goToAudit() {
			uni.navigateTo({
				url: '/pages/house/list?status=pending'
			});
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f7fa;
}

.header {
	height: 120rpx;
	background: white;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 40rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.refresh-btn {
	background: #409EFF;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
}

.content {
	padding: 40rpx;
}

.loading {
	text-align: center;
	padding: 120rpx 0;
	color: #666;
	font-size: 28rpx;
}

.stats-container {
	display: flex;
	flex-direction: column;
	gap: 40rpx;
}

.section {
	background: white;
	border-radius: 12rpx;
	padding: 40rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
	border-bottom: 2rpx solid #eee;
	padding-bottom: 20rpx;
}

/* 总览统计样式 */
.stats-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
	gap: 30rpx;
}

.stat-card {
	border-radius: 12rpx;
	padding: 30rpx;
	color: white;
	display: flex;
	align-items: center;
	position: relative;
	overflow: hidden;
}

.stat-card.total {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.pending {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.approved {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.users {
	background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon {
	font-size: 48rpx;
	margin-right: 20rpx;
}

.stat-info {
	display: flex;
	flex-direction: column;
}

.stat-number {
	font-size: 48rpx;
	font-weight: bold;
	line-height: 1;
}

.stat-label {
	font-size: 24rpx;
	opacity: 0.9;
	margin-top: 8rpx;
}

/* 今日数据样式 */
.today-stats {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(150rpx, 1fr));
	gap: 30rpx;
}

.today-item {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 30rpx;
	text-align: center;
	border: 2rpx solid #e9ecef;
	transition: all 0.3s ease;
}

.today-item:hover {
	transform: translateY(-4rpx);
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.today-icon {
	font-size: 40rpx;
	margin-bottom: 16rpx;
}

.today-info {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.today-number {
	font-size: 36rpx;
	font-weight: bold;
	color: #409EFF;
}

.today-label {
	font-size: 24rpx;
	color: #666;
}

/* 图表样式 */
.chart-container {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.chart-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.chart-bar {
	flex: 1;
	height: 40rpx;
	background: #f0f0f0;
	border-radius: 20rpx;
	overflow: hidden;
	position: relative;
}

.bar-fill {
	height: 100%;
	border-radius: 20rpx;
	transition: width 0.8s ease;
}

.approved-bar {
	background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
}

.pending-bar {
	background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
}

.rejected-bar {
	background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 100%);
}

.chart-label {
	min-width: 120rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.label-text {
	font-size: 26rpx;
	color: #666;
}

.label-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

/* 用户统计样式 */
.user-stats-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
	gap: 30rpx;
}

.user-stat-item {
	border-radius: 12rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.user-stat-item.normal {
	background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.user-stat-item.banned {
	background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.user-stat-icon {
	font-size: 48rpx;
}

.user-stat-info {
	display: flex;
	flex-direction: column;
}

.user-stat-number {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.user-stat-label {
	font-size: 24rpx;
	color: #666;
	margin-top: 8rpx;
}

/* 快捷操作样式 */
.quick-actions {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
	gap: 30rpx;
}

.action-btn {
	background: white;
	border: 2rpx solid #e9ecef;
	border-radius: 12rpx;
	padding: 40rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
	transition: all 0.3s ease;
}

.action-btn:hover {
	transform: translateY(-4rpx);
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.action-btn.house-btn:hover {
	border-color: #409EFF;
	background: #f0f9ff;
}

.action-btn.user-btn:hover {
	border-color: #67c23a;
	background: #f0f9ff;
}

.action-btn.audit-btn:hover {
	border-color: #e6a23c;
	background: #fdf6ec;
}

.action-icon {
	font-size: 48rpx;
}

.action-text {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

/* 响应式设计 */
@media screen and (max-width: 750px) {
	.stats-grid {
		grid-template-columns: repeat(2, 1fr);
	}

	.today-stats {
		grid-template-columns: repeat(2, 1fr);
	}

	.chart-item {
		flex-direction: column;
		align-items: stretch;
		gap: 10rpx;
	}

	.chart-label {
		min-width: auto;
	}

	.user-stats-grid {
		grid-template-columns: 1fr;
	}

	.quick-actions {
		grid-template-columns: 1fr;
	}
}
</style>
