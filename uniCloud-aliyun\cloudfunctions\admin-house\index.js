'use strict';

/**
 * 管理后台房源管理云函数
 * 处理房源审核、管理等功能
 */
exports.main = async (event, context) => {
  const { action, data } = event;

  // 统一返回格式
  const response = {
    code: 0,
    message: '操作成功',
    data: null
  };

  try {
    switch (action) {
      case 'getHouseList':
        return await getHouseList(data);
      case 'auditHouse':
        return await auditHouse(data);
      case 'deleteHouse':
        return await deleteHouse(data);
      case 'setFeatured':
        return await setFeatured(data);
      case 'getHouseStats':
        return await getHouseStats(data);
      default:
        response.code = 1001;
        response.message = '未知操作类型';
        return response;
    }
  } catch (error) {
    console.error('管理后台房源管理云函数错误:', error);
    response.code = 2001;
    response.message = '服务器错误';
    return response;
  }
};

/**
 * 验证管理员权限
 */
async function checkAdminAuth(token) {
  if (!token) {
    return { code: 1002, message: '缺少token' };
  }

  try {
    // 调用管理员认证云函数
    const authResult = await uniCloud.callFunction({
      name: 'admin-auth',
      data: {
        action: 'checkAdminToken',
        data: { token }
      }
    });

    return authResult.result;
  } catch (error) {
    return { code: 2001, message: '权限验证失败' };
  }
}

/**
 * 获取房源列表（管理后台）
 */
async function getHouseList(data) {
  try {
    // 验证管理员权限
    const authCheck = await checkAdminAuth(data.token);
    if (authCheck.code !== 0) {
      return authCheck;
    }

    const {
      page = 1,
      limit = 20,
      status = '',
      keyword = '',
      startDate = '',
      endDate = ''
    } = data;

    const db = uniCloud.database();
    const _ = db.command;
    
    // 构建查询条件
    let whereCondition = {};

    // 状态筛选
    if (status) {
      whereCondition.status = status;
    }

    // 关键词搜索
    if (keyword) {
      whereCondition = _.and([
        whereCondition,
        _.or([
          { title: new RegExp(keyword, 'i') },
          { desc: new RegExp(keyword, 'i') },
          { 'location.address': new RegExp(keyword, 'i') }
        ])
      ]);
    }

    // 时间范围筛选
    if (startDate && endDate) {
      whereCondition.created_at = _.and([
        _.gte(new Date(startDate)),
        _.lte(new Date(endDate))
      ]);
    }

    // 查询房源列表
    const result = await db.collection('house')
      .where(whereCondition)
      .orderBy('created_at', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get();

    // 获取发布者信息
    const ownerIds = [...new Set(result.data.map(house => house.owner_id))];
    const ownersResult = await db.collection('user')
      .where({
        _id: _.in(ownerIds)
      })
      .field({
        nickname: true,
        phone: true,
        avatar: true
      })
      .get();

    // 合并发布者信息
    const houseList = result.data.map(house => {
      const owner = ownersResult.data.find(user => user._id === house.owner_id);
      return {
        ...house,
        owner_info: owner || null
      };
    });

    // 获取总数
    const countResult = await db.collection('house')
      .where(whereCondition)
      .count();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: houseList,
        total: countResult.total,
        page: page,
        limit: limit,
        hasMore: page * limit < countResult.total
      }
    };

  } catch (error) {
    console.error('获取房源列表错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 审核房源
 */
async function auditHouse(data) {
  try {
    // 验证管理员权限
    const authCheck = await checkAdminAuth(data.token);
    if (authCheck.code !== 0) {
      return authCheck;
    }

    const { houseId, status, reason = '' } = data;
    
    if (!houseId || !status) {
      return {
        code: 1001,
        message: '缺少必要参数'
      };
    }

    if (!['approved', 'rejected'].includes(status)) {
      return {
        code: 1001,
        message: '无效的审核状态'
      };
    }

    const db = uniCloud.database();
    
    // 检查房源是否存在
    const houseResult = await db.collection('house').doc(houseId).get();
    
    if (houseResult.data.length === 0) {
      return {
        code: 1004,
        message: '房源不存在'
      };
    }

    // 更新审核状态
    const updateData = {
      status: status,
      updated_at: new Date()
    };

    if (status === 'rejected' && reason) {
      updateData.reject_reason = reason;
    }

    await db.collection('house').doc(houseId).update(updateData);

    return {
      code: 0,
      message: status === 'approved' ? '审核通过' : '审核驳回'
    };

  } catch (error) {
    console.error('审核房源错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 删除房源
 */
async function deleteHouse(data) {
  try {
    // 验证管理员权限
    const authCheck = await checkAdminAuth(data.token);
    if (authCheck.code !== 0) {
      return authCheck;
    }

    const { houseId } = data;
    
    if (!houseId) {
      return {
        code: 1001,
        message: '缺少房源ID'
      };
    }

    const db = uniCloud.database();
    
    // 检查房源是否存在
    const houseResult = await db.collection('house').doc(houseId).get();
    
    if (houseResult.data.length === 0) {
      return {
        code: 1004,
        message: '房源不存在'
      };
    }

    // 删除房源
    await db.collection('house').doc(houseId).remove();

    // 删除相关的收藏记录
    await db.collection('favorites').where({
      house_id: houseId
    }).remove();

    // 删除相关的举报记录
    await db.collection('reports').where({
      house_id: houseId
    }).remove();

    return {
      code: 0,
      message: '删除成功'
    };

  } catch (error) {
    console.error('删除房源错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 设置推荐房源
 */
async function setFeatured(data) {
  try {
    // 验证管理员权限
    const authCheck = await checkAdminAuth(data.token);
    if (authCheck.code !== 0) {
      return authCheck;
    }

    const { houseId, isFeatured } = data;
    
    if (!houseId || typeof isFeatured !== 'boolean') {
      return {
        code: 1001,
        message: '缺少必要参数'
      };
    }

    const db = uniCloud.database();
    
    // 检查房源是否存在且已审核通过
    const houseResult = await db.collection('house').doc(houseId).get();
    
    if (houseResult.data.length === 0) {
      return {
        code: 1004,
        message: '房源不存在'
      };
    }

    const house = houseResult.data[0];
    
    if (house.status !== 'approved') {
      return {
        code: 1005,
        message: '只能推荐已审核通过的房源'
      };
    }

    // 更新推荐状态
    await db.collection('house').doc(houseId).update({
      is_featured: isFeatured,
      updated_at: new Date()
    });

    return {
      code: 0,
      message: isFeatured ? '设置推荐成功' : '取消推荐成功'
    };

  } catch (error) {
    console.error('设置推荐房源错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 获取房源统计数据
 */
async function getHouseStats(data) {
  try {
    // 验证管理员权限
    const authCheck = await checkAdminAuth(data.token);
    if (authCheck.code !== 0) {
      return authCheck;
    }

    const db = uniCloud.database();
    
    // 获取各状态房源数量
    const totalResult = await db.collection('house').count();
    const pendingResult = await db.collection('house').where({ status: 'pending' }).count();
    const approvedResult = await db.collection('house').where({ status: 'approved' }).count();
    const rejectedResult = await db.collection('house').where({ status: 'rejected' }).count();
    const featuredResult = await db.collection('house').where({ is_featured: true }).count();

    // 获取今日新增房源
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayResult = await db.collection('house').where({
      created_at: db.command.gte(today)
    }).count();

    return {
      code: 0,
      message: '获取成功',
      data: {
        total: totalResult.total,
        pending: pendingResult.total,
        approved: approvedResult.total,
        rejected: rejectedResult.total,
        featured: featuredResult.total,
        todayNew: todayResult.total
      }
    };

  } catch (error) {
    console.error('获取房源统计错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}
