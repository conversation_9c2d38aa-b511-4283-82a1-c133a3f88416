<template>
	<view class="container">
		<!-- 顶部操作栏 -->
		<view class="header">
			<view class="header-left">
				<text class="title">用户管理</text>
			</view>
			<view class="header-right">
				<button class="refresh-btn" @click="loadUserList">刷新</button>
			</view>
		</view>
		
		<!-- 筛选栏 -->
		<view class="filter-bar">
			<view class="filter-item">
				<text class="filter-label">状态：</text>
				<picker 
					:value="filterIndex" 
					:range="statusOptions" 
					range-key="text"
					@change="onStatusChange"
				>
					<view class="picker">{{ statusOptions[filterIndex].text }}</view>
				</picker>
			</view>
			<view class="filter-item">
				<text class="filter-label">搜索：</text>
				<input 
					class="search-input" 
					type="text" 
					placeholder="输入用户昵称或手机号" 
					v-model="keyword"
					@confirm="handleSearch"
				/>
				<button class="search-btn" @click="handleSearch">搜索</button>
			</view>
		</view>
		
		<!-- 用户列表 -->
		<view class="content">
			<view v-if="loading" class="loading">
				<text>加载中...</text>
			</view>
			
			<view v-else-if="userList.length === 0" class="empty">
				<text>暂无用户数据</text>
			</view>
			
			<view v-else class="user-list">
				<view 
					v-for="(user, index) in userList" 
					:key="user._id" 
					class="user-item"
				>
					<!-- 用户头像和基本信息 -->
					<view class="user-info">
						<view class="user-avatar">
							<image 
								:src="user.avatar || '/static/default-avatar.png'" 
								mode="aspectFill"
								class="avatar-image"
								@error="onAvatarError"
							></image>
						</view>
						<view class="user-details">
							<text class="user-nickname">{{ user.nickname || '未设置昵称' }}</text>
							<text class="user-phone" v-if="user.phone">📱 {{ user.phone }}</text>
							<text class="user-openid">🆔 {{ user.openid.substring(0, 20) }}...</text>
							<text class="user-time">⏰ 注册时间：{{ formatTime(user.created_at) }}</text>
							<text class="user-login" v-if="user.last_login">🕐 最后登录：{{ formatTime(user.last_login) }}</text>
						</view>
					</view>
					
					<!-- 状态标签 -->
					<view class="status-tag" :class="getStatusClass(user.is_banned)">
						{{ user.is_banned ? '已封禁' : '正常' }}
					</view>
					
					<!-- 封禁原因 -->
					<view v-if="user.is_banned && user.ban_reason" class="ban-reason">
						<text class="ban-label">封禁原因：</text>
						<text class="ban-text">{{ user.ban_reason }}</text>
					</view>
					
					<!-- 用户统计 -->
					<view class="user-stats">
						<view class="stat-item">
							<text class="stat-number">{{ user.house_count || 0 }}</text>
							<text class="stat-label">发布房源</text>
						</view>
						<view class="stat-item">
							<text class="stat-number">{{ user.favorite_count || 0 }}</text>
							<text class="stat-label">收藏房源</text>
						</view>
					</view>
					
					<!-- 操作按钮 -->
					<view class="actions">
						<button 
							v-if="!user.is_banned" 
							class="ban-btn" 
							@click="showBanModal(user._id)"
						>
							封禁用户
						</button>
						<button 
							v-if="user.is_banned" 
							class="unban-btn" 
							@click="unbanUser(user._id)"
						>
							解除封禁
						</button>
						<button class="view-houses-btn" @click="viewUserHouses(user._id)">查看房源</button>
					</view>
				</view>
			</view>
			
			<!-- 分页 -->
			<view v-if="userList.length > 0" class="pagination">
				<button 
					class="page-btn" 
					:disabled="currentPage <= 1"
					@click="changePage(currentPage - 1)"
				>
					上一页
				</button>
				<text class="page-info">{{ currentPage }} / {{ totalPages }}</text>
				<button 
					class="page-btn" 
					:disabled="currentPage >= totalPages"
					@click="changePage(currentPage + 1)"
				>
					下一页
				</button>
			</view>
		</view>
		
		<!-- 封禁用户弹窗 -->
		<view v-if="showBanDialog" class="modal-overlay" @click="hideBanModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">封禁用户</text>
					<text class="modal-close" @click="hideBanModal">×</text>
				</view>
				<view class="modal-body">
					<textarea 
						class="ban-textarea" 
						placeholder="请输入封禁原因..." 
						v-model="banReason"
						maxlength="200"
					></textarea>
				</view>
				<view class="modal-footer">
					<button class="cancel-btn" @click="hideBanModal">取消</button>
					<button class="confirm-btn" @click="confirmBan">确认封禁</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			loading: false,
			userList: [],
			currentPage: 1,
			pageSize: 10,
			total: 0,
			keyword: '',
			filterIndex: 0,
			statusOptions: [
				{ value: '', text: '全部状态' },
				{ value: 'normal', text: '正常用户' },
				{ value: 'banned', text: '已封禁' }
			],
			showBanDialog: false,
			banReason: '',
			currentBanUserId: ''
		}
	},
	
	computed: {
		totalPages() {
			return Math.ceil(this.total / this.pageSize);
		}
	},
	
	onLoad() {
		this.checkAuth();
		this.loadUserList();
	},
	
	methods: {
		// 检查登录状态
		checkAuth() {
			const token = uni.getStorageSync('admin_token');
			if (!token) {
				uni.reLaunch({
					url: '/pages/login/login'
				});
			}
		},
		
		// 加载用户列表
		async loadUserList() {
			this.loading = true;
			try {
				const token = uni.getStorageSync('admin_token');
				const status = this.statusOptions[this.filterIndex].value;
				
				const result = await uniCloud.callFunction({
					name: 'admin-user',
					data: {
						action: 'getUserList',
						data: {
							token,
							page: this.currentPage,
							limit: this.pageSize,
							status,
							keyword: this.keyword
						}
					}
				});
				
				if (result.result.code === 0) {
					this.userList = result.result.data.list;
					this.total = result.result.data.total;
				} else {
					uni.showToast({
						title: result.result.message || '加载失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('加载用户列表失败:', error);
				// 模拟数据用于演示
				this.userList = [
					{
						_id: '1',
						openid: 'wx123456789abcdef',
						nickname: '张三',
						avatar: '',
						phone: '13800138000',
						is_banned: false,
						created_at: Date.now() - 86400000 * 30,
						last_login: Date.now() - 3600000,
						house_count: 3,
						favorite_count: 12
					},
					{
						_id: '2',
						openid: 'wx987654321fedcba',
						nickname: '李四',
						avatar: '',
						phone: '13900139000',
						is_banned: true,
						ban_reason: '发布虚假房源信息',
						created_at: Date.now() - 86400000 * 15,
						last_login: Date.now() - 86400000 * 5,
						house_count: 1,
						favorite_count: 5
					}
				];
				this.total = 2;
			} finally {
				this.loading = false;
			}
		},
		
		// 状态筛选
		onStatusChange(e) {
			this.filterIndex = e.detail.value;
			this.currentPage = 1;
			this.loadUserList();
		},
		
		// 搜索
		handleSearch() {
			this.currentPage = 1;
			this.loadUserList();
		},
		
		// 分页
		changePage(page) {
			if (page < 1 || page > this.totalPages) return;
			this.currentPage = page;
			this.loadUserList();
		},
		
		// 显示封禁弹窗
		showBanModal(userId) {
			this.currentBanUserId = userId;
			this.banReason = '';
			this.showBanDialog = true;
		},
		
		// 隐藏封禁弹窗
		hideBanModal() {
			this.showBanDialog = false;
			this.currentBanUserId = '';
			this.banReason = '';
		},
		
		// 确认封禁
		async confirmBan() {
			if (!this.banReason.trim()) {
				uni.showToast({
					title: '请输入封禁原因',
					icon: 'none'
				});
				return;
			}
			
			try {
				const token = uni.getStorageSync('admin_token');
				
				const result = await uniCloud.callFunction({
					name: 'admin-user',
					data: {
						action: 'banUser',
						data: {
							token,
							userId: this.currentBanUserId,
							reason: this.banReason
						}
					}
				});
				
				if (result.result.code === 0) {
					uni.showToast({
						title: '封禁成功',
						icon: 'success'
					});
					this.loadUserList();
				} else {
					uni.showToast({
						title: result.result.message || '封禁失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('封禁用户失败:', error);
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				});
			}
			
			this.hideBanModal();
		},
		
		// 解除封禁
		async unbanUser(userId) {
			uni.showModal({
				title: '确认解封',
				content: '确定要解除对该用户的封禁吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							const token = uni.getStorageSync('admin_token');
							
							const result = await uniCloud.callFunction({
								name: 'admin-user',
								data: {
									action: 'unbanUser',
									data: {
										token,
										userId
									}
								}
							});
							
							if (result.result.code === 0) {
								uni.showToast({
									title: '解封成功',
									icon: 'success'
								});
								this.loadUserList();
							} else {
								uni.showToast({
									title: result.result.message || '解封失败',
									icon: 'none'
								});
							}
						} catch (error) {
							console.error('解封用户失败:', error);
							uni.showToast({
								title: '网络错误',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		
		// 查看用户房源
		viewUserHouses(userId) {
			uni.navigateTo({
				url: `/pages/house/list?userId=${userId}`
			});
		},
		
		// 获取状态样式类
		getStatusClass(isBanned) {
			return isBanned ? 'status-banned' : 'status-normal';
		},
		
		// 格式化时间
		formatTime(timestamp) {
			if (!timestamp) return '';
			const date = new Date(timestamp);
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
		},
		
		// 头像加载失败处理
		onAvatarError(e) {
			console.log('头像加载失败:', e);
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f7fa;
}

.header {
	height: 120rpx;
	background: white;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 40rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.refresh-btn {
	background: #409EFF;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
}

.filter-bar {
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	align-items: center;
	gap: 40rpx;
	border-bottom: 2rpx solid #eee;
}

.filter-item {
	display: flex;
	align-items: center;
}

.filter-label {
	font-size: 28rpx;
	color: #666;
	margin-right: 16rpx;
}

.picker {
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
	border-radius: 8rpx;
	padding: 16rpx 24rpx;
	font-size: 28rpx;
	min-width: 160rpx;
	text-align: center;
}

.search-input {
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
	border-radius: 8rpx;
	padding: 16rpx 24rpx;
	font-size: 28rpx;
	width: 400rpx;
	margin-right: 16rpx;
}

.search-btn {
	background: #409EFF;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
}

.content {
	padding: 40rpx;
}

.loading, .empty {
	text-align: center;
	padding: 120rpx 0;
	color: #666;
	font-size: 28rpx;
}

.user-list {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.user-item {
	background: white;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
	position: relative;
}

.user-info {
	display: flex;
	margin-bottom: 20rpx;
}

.user-avatar {
	width: 120rpx;
	height: 120rpx;
	margin-right: 30rpx;
	flex-shrink: 0;
}

.avatar-image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 4rpx solid #e9ecef;
}

.user-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.user-nickname {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.user-phone,
.user-openid,
.user-time,
.user-login {
	font-size: 26rpx;
	color: #666;
}

.status-tag {
	position: absolute;
	top: 30rpx;
	right: 30rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: white;
}

.status-normal {
	background: #27ae60;
}

.status-banned {
	background: #e74c3c;
}

.ban-reason {
	margin: 20rpx 0;
	padding: 20rpx;
	background: #fff5f5;
	border: 2rpx solid #fed7d7;
	border-radius: 8rpx;
}

.ban-label {
	font-size: 26rpx;
	color: #e53e3e;
	font-weight: bold;
}

.ban-text {
	font-size: 26rpx;
	color: #e53e3e;
}

.user-stats {
	display: flex;
	gap: 40rpx;
	margin: 20rpx 0;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.stat-number {
	font-size: 32rpx;
	font-weight: bold;
	color: #409EFF;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}

.actions {
	display: flex;
	gap: 20rpx;
	margin-top: 20rpx;
}

.ban-btn,
.unban-btn,
.view-houses-btn {
	padding: 16rpx 32rpx;
	border: none;
	border-radius: 8rpx;
	font-size: 26rpx;
	cursor: pointer;
}

.ban-btn {
	background: #e74c3c;
	color: white;
}

.unban-btn {
	background: #27ae60;
	color: white;
}

.view-houses-btn {
	background: #409EFF;
	color: white;
}

.pagination {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 30rpx;
	margin-top: 40rpx;
	padding: 40rpx 0;
}

.page-btn {
	background: #409EFF;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
}

.page-btn:disabled {
	background: #ccc;
	cursor: not-allowed;
}

.page-info {
	font-size: 28rpx;
	color: #666;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	background: white;
	border-radius: 12rpx;
	width: 80%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	border-bottom: 2rpx solid #eee;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	font-size: 48rpx;
	color: #999;
	cursor: pointer;
}

.modal-body {
	padding: 40rpx;
}

.ban-textarea {
	width: 100%;
	min-height: 200rpx;
	border: 2rpx solid #e9ecef;
	border-radius: 8rpx;
	padding: 20rpx;
	font-size: 28rpx;
	resize: none;
}

.modal-footer {
	display: flex;
	justify-content: flex-end;
	gap: 20rpx;
	padding: 30rpx 40rpx;
	border-top: 2rpx solid #eee;
}

.cancel-btn,
.confirm-btn {
	padding: 16rpx 32rpx;
	border: none;
	border-radius: 8rpx;
	font-size: 28rpx;
}

.cancel-btn {
	background: #95a5a6;
	color: white;
}

.confirm-btn {
	background: #e74c3c;
	color: white;
}

/* 响应式设计 */
@media screen and (max-width: 750px) {
	.filter-bar {
		flex-direction: column;
		gap: 20rpx;
		align-items: stretch;
	}

	.filter-item {
		justify-content: space-between;
	}

	.search-input {
		width: 100%;
		margin-right: 0;
		margin-bottom: 16rpx;
	}

	.user-info {
		flex-direction: column;
		align-items: center;
		text-align: center;
	}

	.user-avatar {
		margin-right: 0;
		margin-bottom: 20rpx;
	}

	.user-stats {
		justify-content: center;
	}

	.actions {
		flex-wrap: wrap;
		justify-content: center;
	}

	.modal-content {
		width: 95%;
	}
}
</style>
