<template>
	<view class="login-container">
		<view class="login-box">
			<view class="login-header">
				<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
				<text class="title">租房管理后台</text>
				<text class="subtitle">Admin Management System</text>
			</view>
			
			<view class="login-form">
				<view class="form-item">
					<view class="input-wrapper">
						<text class="iconfont icon-user"></text>
						<input 
							class="input" 
							type="text" 
							placeholder="请输入用户名" 
							v-model="formData.username"
							:disabled="loading"
						/>
					</view>
				</view>
				
				<view class="form-item">
					<view class="input-wrapper">
						<text class="iconfont icon-lock"></text>
						<input 
							class="input" 
							type="password" 
							placeholder="请输入密码" 
							v-model="formData.password"
							:disabled="loading"
							@confirm="handleLogin"
						/>
					</view>
				</view>
				
				<button 
					class="login-btn" 
					:class="{ 'loading': loading }"
					:disabled="loading"
					@click="handleLogin"
				>
					<text v-if="loading">登录中...</text>
					<text v-else>登录</text>
				</button>
			</view>
			
			<view class="login-footer">
				<text class="copyright">© 2024 租房管理平台</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			formData: {
				username: '',
				password: ''
			},
			loading: false
		}
	},
	
	onLoad() {
		// 检查是否已登录
		this.checkLogin();
	},
	
	methods: {
		// 检查登录状态
		checkLogin() {
			const token = uni.getStorageSync('admin_token');
			if (token) {
				// 验证token有效性
				this.verifyToken(token);
			}
		},
		
		// 验证token
		async verifyToken(token) {
			try {
				const result = await uniCloud.callFunction({
					name: 'admin-auth',
					data: {
						action: 'checkAdminToken',
						data: { token }
					}
				});
				
				if (result.result.code === 0) {
					// token有效，直接跳转到首页
					uni.reLaunch({
						url: '/pages/index/index'
					});
				} else {
					// token无效，清除本地存储
					uni.removeStorageSync('admin_token');
					uni.removeStorageSync('admin_info');
				}
			} catch (error) {
				console.error('验证token失败:', error);
				uni.removeStorageSync('admin_token');
				uni.removeStorageSync('admin_info');
			}
		},
		
		// 处理登录
		async handleLogin() {
			if (!this.formData.username.trim()) {
				uni.showToast({
					title: '请输入用户名',
					icon: 'none'
				});
				return;
			}
			
			if (!this.formData.password.trim()) {
				uni.showToast({
					title: '请输入密码',
					icon: 'none'
				});
				return;
			}
			
			this.loading = true;
			
			try {
				const result = await uniCloud.callFunction({
					name: 'admin-auth',
					data: {
						action: 'adminLogin',
						data: {
							username: this.formData.username.trim(),
							password: this.formData.password.trim()
						}
					}
				});
				
				if (result.result.code === 0) {
					// 登录成功
					const { token, adminInfo } = result.result.data;
					
					// 保存登录信息
					uni.setStorageSync('admin_token', token);
					uni.setStorageSync('admin_info', adminInfo);
					
					uni.showToast({
						title: '登录成功',
						icon: 'success'
					});
					
					// 跳转到首页
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/index/index'
						});
					}, 1500);
					
				} else {
					// 登录失败
					uni.showToast({
						title: result.result.message || '登录失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('登录失败:', error);
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		}
	}
}
</script>

<style scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.login-box {
	width: 100%;
	max-width: 400px;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10px);
}

.login-header {
	text-align: center;
	margin-bottom: 60rpx;
}

.logo {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 20rpx;
}

.title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.subtitle {
	display: block;
	font-size: 28rpx;
	color: #666;
}

.login-form {
	margin-bottom: 40rpx;
}

.form-item {
	margin-bottom: 30rpx;
}

.input-wrapper {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 0 20rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.input-wrapper:focus-within {
	border-color: #409EFF;
	background: #fff;
	box-shadow: 0 0 0 6rpx rgba(64, 158, 255, 0.1);
}

.iconfont {
	font-size: 32rpx;
	color: #999;
	margin-right: 20rpx;
	width: 32rpx;
	text-align: center;
}

.input {
	flex: 1;
	height: 88rpx;
	font-size: 32rpx;
	color: #333;
	background: transparent;
}

.login-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #409EFF, #5dade2);
	color: white;
	border: none;
	border-radius: 12rpx;
	font-size: 32rpx;
	font-weight: bold;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
}

.login-btn:not(.loading):active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.3);
}

.login-btn.loading {
	opacity: 0.7;
	background: #ccc;
}

.login-footer {
	text-align: center;
	margin-top: 40rpx;
}

.copyright {
	font-size: 24rpx;
	color: #999;
}

/* 图标字体 */
.icon-user::before {
	content: "👤";
}

.icon-lock::before {
	content: "🔒";
}
</style>
