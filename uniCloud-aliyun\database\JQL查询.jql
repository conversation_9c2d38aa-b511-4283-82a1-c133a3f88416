// 本文件用于，使用JQL语法操作项目关联的uniCloud空间的数据库，方便开发调试和远程数据库管理
// 编写clientDB的js API（也支持常规js语法，比如var），可以对云数据库进行增删改查操作。不支持uniCloud-db组件写法
// 可以全部运行，也可以选中部分代码运行。点击工具栏上的运行按钮或者按下【F5】键运行代码
// 如果文档中存在多条JQL语句，只有最后一条语句生效
// 如果混写了普通js，最后一条语句需是数据库操作语句
// 此处代码运行不受DB Schema的权限控制，移植代码到实际业务中注意在schema中配好permission
// 不支持clientDB的action
// 数据库查询有最大返回条数限制，详见：https://uniapp.dcloud.net.cn/uniCloud/cf-database.html#limit
// 详细JQL语法，请参考：https://uniapp.dcloud.net.cn/uniCloud/jql.html

// 租房平台数据库测试查询

// 查询所有房源
// db.collection('house').get();

// 查询用户信息
// db.collection('user').get();

// 查询管理员信息
// db.collection('admin').get();

// 查询收藏记录
// db.collection('favorites').get();

// 查询举报记录
// db.collection('reports').get();

// 下面示例查询uni-id-users表的所有数据
db.collection('uni-id-users').get();
