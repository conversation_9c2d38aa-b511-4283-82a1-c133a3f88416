{"bsonType": "object", "description": "用户信息集合", "required": ["openid", "nickname"], "properties": {"_id": {"description": "用户ID"}, "openid": {"bsonType": "string", "description": "微信openid", "title": "微信openid"}, "nickname": {"bsonType": "string", "description": "微信昵称", "maxLength": 50, "title": "微信昵称"}, "avatar": {"bsonType": "string", "description": "头像URL", "format": "url", "title": "头像"}, "phone": {"bsonType": "string", "description": "手机号", "pattern": "^1[3-9]\\d{9}$", "title": "手机号"}, "is_banned": {"bsonType": "bool", "description": "是否被封禁", "defaultValue": false, "title": "是否被封禁"}, "ban_reason": {"bsonType": "string", "description": "封禁原因", "title": "封禁原因"}, "created_at": {"bsonType": "timestamp", "description": "注册时间", "forceDefaultValue": {"$env": "now"}, "title": "注册时间"}, "last_login": {"bsonType": "timestamp", "description": "最后登录时间", "forceDefaultValue": {"$env": "now"}, "title": "最后登录时间"}}, "permission": {"read": "auth.uid == doc._id || 'admin' in auth.role", "create": "auth.uid != null", "update": "auth.uid == doc._id || 'admin' in auth.role", "delete": "'admin' in auth.role"}, "index": [{"IndexName": "openid", "MgoKeySchema": {"openid": 1}, "unique": true}, {"IndexName": "phone", "MgoKeySchema": {"phone": 1}}, {"IndexName": "created_at", "MgoKeySchema": {"created_at": -1}}]}