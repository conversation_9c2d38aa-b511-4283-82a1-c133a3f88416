{"bsonType": "object", "description": "举报记录集合", "required": ["house_id", "user_id", "reason"], "properties": {"_id": {"description": "举报ID"}, "house_id": {"bsonType": "string", "description": "房源ID", "foreignKey": "house._id", "title": "房源ID"}, "user_id": {"bsonType": "string", "description": "举报用户ID", "foreignKey": "user._id", "title": "举报用户ID"}, "reason": {"bsonType": "string", "description": "举报原因", "enum": ["虚假信息", "重复发布", "价格欺诈", "图片不符", "联系方式无效", "其他"], "title": "举报原因"}, "desc": {"bsonType": "string", "description": "详细描述", "maxLength": 500, "title": "详细描述"}, "status": {"bsonType": "string", "description": "处理状态", "enum": ["pending", "handled"], "defaultValue": "pending", "title": "处理状态"}, "created_at": {"bsonType": "timestamp", "description": "举报时间", "forceDefaultValue": {"$env": "now"}, "title": "举报时间"}}, "permission": {"read": "'admin' in auth.role", "create": "auth.uid != null", "update": "'admin' in auth.role", "delete": "'admin' in auth.role"}, "index": [{"IndexName": "house_id", "MgoKeySchema": {"house_id": 1}}, {"IndexName": "status_created", "MgoKeySchema": {"status": 1, "created_at": -1}}, {"IndexName": "user_id", "MgoKeySchema": {"user_id": 1}}]}