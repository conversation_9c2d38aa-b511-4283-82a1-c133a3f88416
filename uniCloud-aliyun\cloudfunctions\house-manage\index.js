'use strict';

const uniID = require('uni-id-common');

/**
 * 房源管理云函数
 * 处理房源的增删改查、搜索筛选等功能
 */
exports.main = async (event, context) => {
  const { action, data } = event;
  const uniIdIns = uniID.createInstance({
    context
  });

  // 统一返回格式
  const response = {
    code: 0,
    message: '操作成功',
    data: null
  };

  try {
    switch (action) {
      case 'getHouseList':
        return await getHouseList(data, uniIdIns);
      case 'getHouseDetail':
        return await getHouseDetail(data, uniIdIns);
      case 'publishHouse':
        return await publishHouse(data, uniIdIns);
      case 'updateHouse':
        return await updateHouse(data, uniIdIns);
      case 'deleteHouse':
        return await deleteHouse(data, uniIdIns);
      case 'getFeaturedHouses':
        return await getFeaturedHouses(data, uniIdIns);
      case 'getMyHouses':
        return await getMyHouses(data, uniIdIns);
      case 'searchHouses':
        return await searchHouses(data, uniIdIns);
      default:
        response.code = 1001;
        response.message = '未知操作类型';
        return response;
    }
  } catch (error) {
    console.error('房源管理云函数错误:', error);
    response.code = 2001;
    response.message = '服务器错误';
    return response;
  }
};

/**
 * 获取房源列表
 */
async function getHouseList(data, uniIdIns) {
  try {
    const {
      page = 1,
      limit = 10,
      keyword = '',
      district = '',
      priceMin = 0,
      priceMax = 999999,
      type = '',
      sortBy = 'time_desc'
    } = data;

    const db = uniCloud.database();
    const _ = db.command;
    
    // 构建查询条件
    let whereCondition = {
      status: 'approved'  // 只显示已审核通过的房源
    };

    // 关键词搜索
    if (keyword) {
      whereCondition = _.and([
        whereCondition,
        _.or([
          { title: new RegExp(keyword, 'i') },
          { desc: new RegExp(keyword, 'i') },
          { 'location.address': new RegExp(keyword, 'i') }
        ])
      ]);
    }

    // 区域筛选
    if (district) {
      whereCondition['location.district'] = district;
    }

    // 价格筛选
    if (priceMin > 0 || priceMax < 999999) {
      whereCondition.price = _.and([
        _.gte(priceMin),
        _.lte(priceMax)
      ]);
    }

    // 房型筛选
    if (type) {
      whereCondition.type = type;
    }

    // 排序设置
    let orderBy = {};
    switch (sortBy) {
      case 'price_asc':
        orderBy.price = 1;
        break;
      case 'price_desc':
        orderBy.price = -1;
        break;
      case 'time_desc':
      default:
        orderBy.created_at = -1;
        break;
    }

    // 查询房源列表
    const result = await db.collection('house')
      .where(whereCondition)
      .orderBy(orderBy)
      .skip((page - 1) * limit)
      .limit(limit)
      .field({
        title: true,
        images: true,
        location: true,
        price: true,
        type: true,
        area: true,
        config: true,
        view_count: true,
        created_at: true
      })
      .get();

    // 获取总数
    const countResult = await db.collection('house')
      .where(whereCondition)
      .count();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: result.data,
        total: countResult.total,
        page: page,
        limit: limit,
        hasMore: page * limit < countResult.total
      }
    };

  } catch (error) {
    console.error('获取房源列表错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 获取房源详情
 */
async function getHouseDetail(data, uniIdIns) {
  try {
    const { houseId } = data;

    if (!houseId) {
      return {
        code: 1001,
        message: '缺少房源ID'
      };
    }

    const db = uniCloud.database();
    
    // 获取房源详情
    const houseResult = await db.collection('house').doc(houseId).get();

    if (houseResult.data.length === 0) {
      return {
        code: 1004,
        message: '房源不存在'
      };
    }

    const house = houseResult.data[0];

    // 增加浏览次数
    await db.collection('house').doc(houseId).update({
      view_count: db.command.inc(1)
    });

    // 获取发布者信息
    const userResult = await db.collection('user').doc(house.owner_id)
      .field({
        nickname: true,
        avatar: true
      })
      .get();

    if (userResult.data.length > 0) {
      house.owner_info = userResult.data[0];
    }

    return {
      code: 0,
      message: '获取成功',
      data: house
    };

  } catch (error) {
    console.error('获取房源详情错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 发布房源
 */
async function publishHouse(data, uniIdIns) {
  try {
    // 验证token
    const checkTokenResult = await uniIdIns.checkToken(data.token);
    
    if (checkTokenResult.errCode !== 0) {
      return {
        code: 1002,
        message: '用户未登录或token已过期'
      };
    }

    // 验证必填字段
    const { title, desc, location, price, type } = data;
    
    if (!title || !desc || !location || !price || !type) {
      return {
        code: 1001,
        message: '缺少必填字段'
      };
    }

    const db = uniCloud.database();
    
    // 构建房源数据
    const houseData = {
      title,
      desc,
      images: data.images || [],
      location,
      price,
      type,
      area: data.area || 0,
      floor: data.floor || '',
      config: data.config || [],
      contact: data.contact || {},
      owner_id: checkTokenResult.uid,
      status: 'pending',
      is_featured: false,
      view_count: 0,
      created_at: new Date(),
      updated_at: new Date()
    };

    const result = await db.collection('house').add(houseData);

    return {
      code: 0,
      message: '发布成功，等待审核',
      data: {
        houseId: result.id
      }
    };

  } catch (error) {
    console.error('发布房源错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 获取推荐房源
 */
async function getFeaturedHouses(data, uniIdIns) {
  try {
    const { limit = 6 } = data;

    const db = uniCloud.database();
    
    const result = await db.collection('house')
      .where({
        status: 'approved',
        is_featured: true
      })
      .orderBy('created_at', 'desc')
      .limit(limit)
      .field({
        title: true,
        images: true,
        location: true,
        price: true,
        type: true,
        view_count: true
      })
      .get();

    return {
      code: 0,
      message: '获取成功',
      data: result.data
    };

  } catch (error) {
    console.error('获取推荐房源错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 获取我的房源
 */
async function getMyHouses(data, uniIdIns) {
  try {
    // 验证token
    const checkTokenResult = await uniIdIns.checkToken(data.token);

    if (checkTokenResult.errCode !== 0) {
      return {
        code: 1002,
        message: '用户未登录或token已过期'
      };
    }

    const { page = 1, limit = 10 } = data;
    const db = uniCloud.database();

    const result = await db.collection('house')
      .where({
        owner_id: checkTokenResult.uid
      })
      .orderBy('created_at', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get();

    const countResult = await db.collection('house')
      .where({
        owner_id: checkTokenResult.uid
      })
      .count();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: result.data,
        total: countResult.total,
        page: page,
        hasMore: page * limit < countResult.total
      }
    };

  } catch (error) {
    console.error('获取我的房源错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 更新房源
 */
async function updateHouse(data, uniIdIns) {
  try {
    // 验证token
    const checkTokenResult = await uniIdIns.checkToken(data.token);

    if (checkTokenResult.errCode !== 0) {
      return {
        code: 1002,
        message: '用户未登录或token已过期'
      };
    }

    const { houseId } = data;

    if (!houseId) {
      return {
        code: 1001,
        message: '缺少房源ID'
      };
    }

    const db = uniCloud.database();

    // 检查房源是否存在且属于当前用户
    const houseResult = await db.collection('house').doc(houseId).get();

    if (houseResult.data.length === 0) {
      return {
        code: 1004,
        message: '房源不存在'
      };
    }

    const house = houseResult.data[0];

    if (house.owner_id !== checkTokenResult.uid) {
      return {
        code: 1003,
        message: '无权限编辑此房源'
      };
    }

    // 构建更新数据
    const updateData = {
      updated_at: new Date()
    };

    // 只更新允许的字段
    const allowedFields = ['title', 'desc', 'images', 'location', 'price', 'type', 'area', 'floor', 'config', 'contact'];
    allowedFields.forEach(field => {
      if (data[field] !== undefined) {
        updateData[field] = data[field];
      }
    });

    // 如果修改了内容，重新设置为待审核状态
    if (Object.keys(updateData).length > 1) {
      updateData.status = 'pending';
    }

    await db.collection('house').doc(houseId).update(updateData);

    return {
      code: 0,
      message: '更新成功'
    };

  } catch (error) {
    console.error('更新房源错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 删除房源
 */
async function deleteHouse(data, uniIdIns) {
  try {
    // 验证token
    const checkTokenResult = await uniIdIns.checkToken(data.token);

    if (checkTokenResult.errCode !== 0) {
      return {
        code: 1002,
        message: '用户未登录或token已过期'
      };
    }

    const { houseId } = data;

    if (!houseId) {
      return {
        code: 1001,
        message: '缺少房源ID'
      };
    }

    const db = uniCloud.database();

    // 检查房源是否存在且属于当前用户
    const houseResult = await db.collection('house').doc(houseId).get();

    if (houseResult.data.length === 0) {
      return {
        code: 1004,
        message: '房源不存在'
      };
    }

    const house = houseResult.data[0];

    if (house.owner_id !== checkTokenResult.uid) {
      return {
        code: 1003,
        message: '无权限删除此房源'
      };
    }

    // 删除房源
    await db.collection('house').doc(houseId).remove();

    // 删除相关的收藏记录
    await db.collection('favorites').where({
      house_id: houseId
    }).remove();

    return {
      code: 0,
      message: '删除成功'
    };

  } catch (error) {
    console.error('删除房源错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 搜索房源
 */
async function searchHouses(data, uniIdIns) {
  try {
    const { keyword, page = 1, limit = 10 } = data;

    if (!keyword) {
      return {
        code: 1001,
        message: '缺少搜索关键词'
      };
    }

    const db = uniCloud.database();
    const _ = db.command;

    // 构建搜索条件
    const whereCondition = _.and([
      { status: 'approved' },
      _.or([
        { title: new RegExp(keyword, 'i') },
        { desc: new RegExp(keyword, 'i') },
        { 'location.address': new RegExp(keyword, 'i') },
        { 'location.district': new RegExp(keyword, 'i') }
      ])
    ]);

    const result = await db.collection('house')
      .where(whereCondition)
      .orderBy('created_at', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .field({
        title: true,
        images: true,
        location: true,
        price: true,
        type: true,
        area: true,
        view_count: true,
        created_at: true
      })
      .get();

    const countResult = await db.collection('house')
      .where(whereCondition)
      .count();

    return {
      code: 0,
      message: '搜索成功',
      data: {
        list: result.data,
        total: countResult.total,
        page: page,
        hasMore: page * limit < countResult.total,
        keyword
      }
    };

  } catch (error) {
    console.error('搜索房源错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}
