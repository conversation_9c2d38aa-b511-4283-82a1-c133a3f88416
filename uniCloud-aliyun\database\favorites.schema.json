{"bsonType": "object", "description": "收藏记录集合", "required": ["user_id", "house_id"], "properties": {"_id": {"description": "收藏ID"}, "user_id": {"bsonType": "string", "description": "用户ID", "foreignKey": "user._id", "title": "用户ID"}, "house_id": {"bsonType": "string", "description": "房源ID", "foreignKey": "house._id", "title": "房源ID"}, "created_at": {"bsonType": "timestamp", "description": "收藏时间", "forceDefaultValue": {"$env": "now"}, "title": "收藏时间"}}, "permission": {"read": "auth.uid == doc.user_id || 'admin' in auth.role", "create": "auth.uid != null", "update": false, "delete": "auth.uid == doc.user_id || 'admin' in auth.role"}, "index": [{"IndexName": "user_house", "MgoKeySchema": {"user_id": 1, "house_id": 1}, "unique": true}, {"IndexName": "user_id", "MgoKeySchema": {"user_id": 1, "created_at": -1}}, {"IndexName": "house_id", "MgoKeySchema": {"house_id": 1}}]}