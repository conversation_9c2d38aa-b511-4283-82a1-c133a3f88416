# 租房管理后台使用说明

## 项目概述

这是一个基于 uniapp + Vue2 + UniCloud 开发的租房管理后台系统，提供了完整的房源管理、用户管理和数据统计功能。

## 功能特性

### 1. 管理员登录
- 安全的管理员身份验证
- Token 自动验证和续期
- 登录状态持久化

### 2. 数据统计仪表板
- 房源总览统计（总数、待审核、已通过、已驳回）
- 用户统计（总用户数、正常用户、封禁用户）
- 今日数据统计
- 可视化图表展示
- 快捷操作入口

### 3. 房源管理
- 房源列表查看（支持分页）
- 房源状态筛选（全部、待审核、已通过、已驳回）
- 关键词搜索功能
- 房源审核（通过/驳回）
- 推荐房源设置
- 房源删除功能
- 详细的房源信息展示

### 4. 用户管理
- 用户列表查看（支持分页）
- 用户状态筛选（全部、正常、已封禁）
- 用户搜索功能
- 用户封禁/解封操作
- 用户统计信息展示
- 查看用户发布的房源

## 技术架构

### 前端技术栈
- **框架**: uniapp + Vue2
- **UI**: 自定义组件 + 原生样式
- **状态管理**: 本地存储 (localStorage)
- **网络请求**: uniCloud.callFunction

### 后端技术栈
- **云开发平台**: UniCloud (阿里云版)
- **数据库**: MongoDB (云数据库)
- **云函数**: Node.js
- **身份验证**: 自定义 Token 验证

### 云函数列表
1. **admin-auth**: 管理员身份验证
2. **admin-house**: 房源管理相关操作
3. **admin-user**: 用户管理相关操作
4. **house-manage**: 前端房源操作
5. **user-auth**: 用户身份验证
6. **favorite-manage**: 收藏管理
7. **file-upload**: 文件上传

## 页面结构

```
pages/
├── login/login.vue          # 管理员登录页
├── index/index.vue          # 主页面（仪表板概览）
├── dashboard/dashboard.vue  # 详细数据统计页
├── house/list.vue          # 房源管理页
└── user/list.vue           # 用户管理页
```

## 使用指南

### 1. 登录系统
- 默认管理员账号：`admin`
- 默认密码：`123456`
- 登录后会自动保存 Token，下次访问无需重新登录

### 2. 查看数据统计
- 主页面显示核心统计数据
- 点击"查看详细统计"进入详细统计页面
- 支持数据刷新功能

### 3. 管理房源
- 在房源管理页面可以查看所有房源
- 支持按状态筛选和关键词搜索
- 对待审核房源进行审核操作
- 设置推荐房源
- 删除不当房源

### 4. 管理用户
- 在用户管理页面查看所有用户
- 支持按状态筛选和搜索
- 对违规用户进行封禁操作
- 查看用户发布的房源

## 部署说明

### 1. 环境要求
- HBuilderX 3.0+
- UniCloud 阿里云版
- Node.js 14+

### 2. 部署步骤
1. 在 HBuilderX 中打开项目
2. 配置 UniCloud 服务空间
3. 上传云函数到服务空间
4. 初始化数据库（运行 `初始化数据库.js`）
5. 运行项目到浏览器或小程序

### 3. 数据库初始化
运行项目根目录下的 `初始化数据库.js` 文件，会自动：
- 创建默认管理员账号
- 创建示例房源数据
- 设置数据库索引

## 安全特性

1. **身份验证**: 基于 Token 的身份验证机制
2. **权限控制**: 管理员权限验证
3. **数据验证**: 前后端双重数据验证
4. **SQL注入防护**: 使用 UniCloud 数据库查询防护
5. **XSS防护**: 输入数据转义处理

## 扩展功能

系统预留了扩展接口，可以轻松添加：
- 角色权限管理
- 操作日志记录
- 数据导出功能
- 消息通知系统
- 多租户支持

## 注意事项

1. 首次使用需要初始化数据库
2. 确保 UniCloud 服务空间配置正确
3. 云函数需要正确上传并部署
4. 建议定期备份数据库数据
5. 生产环境请修改默认管理员密码

## 技术支持

如有问题，请检查：
1. UniCloud 服务空间是否正常
2. 云函数是否正确部署
3. 数据库是否正确初始化
4. 网络连接是否正常

## 更新日志

### v1.0.0 (2024-08-01)
- 完成基础管理后台功能
- 实现房源管理和用户管理
- 添加数据统计功能
- 完善响应式设计
