'use strict';

const uniID = require('uni-id-common');

/**
 * 收藏管理云函数
 * 处理房源收藏、取消收藏、收藏列表等功能
 */
exports.main = async (event, context) => {
  const { action, data } = event;
  const uniIdIns = uniID.createInstance({
    context
  });

  // 统一返回格式
  const response = {
    code: 0,
    message: '操作成功',
    data: null
  };

  try {
    switch (action) {
      case 'addFavorite':
        return await addFavorite(data, uniIdIns);
      case 'removeFavorite':
        return await removeFavorite(data, uniIdIns);
      case 'getFavoriteList':
        return await getFavoriteList(data, uniIdIns);
      case 'checkFavorite':
        return await checkFavorite(data, uniIdIns);
      default:
        response.code = 1001;
        response.message = '未知操作类型';
        return response;
    }
  } catch (error) {
    console.error('收藏管理云函数错误:', error);
    response.code = 2001;
    response.message = '服务器错误';
    return response;
  }
};

/**
 * 添加收藏
 */
async function addFavorite(data, uniIdIns) {
  try {
    // 验证token
    const checkTokenResult = await uniIdIns.checkToken(data.token);
    
    if (checkTokenResult.errCode !== 0) {
      return {
        code: 1002,
        message: '用户未登录或token已过期'
      };
    }

    const { houseId } = data;
    
    if (!houseId) {
      return {
        code: 1001,
        message: '缺少房源ID'
      };
    }

    const db = uniCloud.database();
    
    // 检查房源是否存在
    const houseResult = await db.collection('house').doc(houseId).get();
    
    if (houseResult.data.length === 0) {
      return {
        code: 1004,
        message: '房源不存在'
      };
    }

    // 检查是否已经收藏
    const existFavorite = await db.collection('favorites').where({
      user_id: checkTokenResult.uid,
      house_id: houseId
    }).get();

    if (existFavorite.data.length > 0) {
      return {
        code: 1005,
        message: '已经收藏过此房源'
      };
    }

    // 添加收藏记录
    const favoriteData = {
      user_id: checkTokenResult.uid,
      house_id: houseId,
      created_at: new Date()
    };

    const result = await db.collection('favorites').add(favoriteData);

    return {
      code: 0,
      message: '收藏成功',
      data: {
        favoriteId: result.id
      }
    };

  } catch (error) {
    console.error('添加收藏错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 取消收藏
 */
async function removeFavorite(data, uniIdIns) {
  try {
    // 验证token
    const checkTokenResult = await uniIdIns.checkToken(data.token);
    
    if (checkTokenResult.errCode !== 0) {
      return {
        code: 1002,
        message: '用户未登录或token已过期'
      };
    }

    const { houseId } = data;
    
    if (!houseId) {
      return {
        code: 1001,
        message: '缺少房源ID'
      };
    }

    const db = uniCloud.database();
    
    // 查找收藏记录
    const favoriteResult = await db.collection('favorites').where({
      user_id: checkTokenResult.uid,
      house_id: houseId
    }).get();

    if (favoriteResult.data.length === 0) {
      return {
        code: 1004,
        message: '收藏记录不存在'
      };
    }

    // 删除收藏记录
    await db.collection('favorites').doc(favoriteResult.data[0]._id).remove();

    return {
      code: 0,
      message: '取消收藏成功'
    };

  } catch (error) {
    console.error('取消收藏错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 获取收藏列表
 */
async function getFavoriteList(data, uniIdIns) {
  try {
    // 验证token
    const checkTokenResult = await uniIdIns.checkToken(data.token);
    
    if (checkTokenResult.errCode !== 0) {
      return {
        code: 1002,
        message: '用户未登录或token已过期'
      };
    }

    const { page = 1, limit = 10 } = data;
    const db = uniCloud.database();
    
    // 获取收藏列表，联表查询房源信息
    const result = await db.collection('favorites')
      .where({
        user_id: checkTokenResult.uid
      })
      .orderBy('created_at', 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get();

    // 获取房源详细信息
    const houseIds = result.data.map(item => item.house_id);
    
    if (houseIds.length > 0) {
      const housesResult = await db.collection('house')
        .where({
          _id: db.command.in(houseIds)
        })
        .field({
          title: true,
          images: true,
          location: true,
          price: true,
          type: true,
          area: true,
          status: true,
          view_count: true,
          created_at: true
        })
        .get();

      // 合并收藏信息和房源信息
      const favoriteList = result.data.map(favorite => {
        const house = housesResult.data.find(h => h._id === favorite.house_id);
        return {
          ...favorite,
          house_info: house || null
        };
      }).filter(item => item.house_info); // 过滤掉房源已删除的收藏
    }

    // 获取总数
    const countResult = await db.collection('favorites')
      .where({
        user_id: checkTokenResult.uid
      })
      .count();

    return {
      code: 0,
      message: '获取成功',
      data: {
        list: favoriteList || [],
        total: countResult.total,
        page: page,
        hasMore: page * limit < countResult.total
      }
    };

  } catch (error) {
    console.error('获取收藏列表错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 检查是否已收藏
 */
async function checkFavorite(data, uniIdIns) {
  try {
    // 验证token
    const checkTokenResult = await uniIdIns.checkToken(data.token);
    
    if (checkTokenResult.errCode !== 0) {
      return {
        code: 1002,
        message: '用户未登录或token已过期'
      };
    }

    const { houseId } = data;
    
    if (!houseId) {
      return {
        code: 1001,
        message: '缺少房源ID'
      };
    }

    const db = uniCloud.database();
    
    // 检查是否已收藏
    const favoriteResult = await db.collection('favorites').where({
      user_id: checkTokenResult.uid,
      house_id: houseId
    }).get();

    return {
      code: 0,
      message: '检查成功',
      data: {
        isFavorited: favoriteResult.data.length > 0
      }
    };

  } catch (error) {
    console.error('检查收藏状态错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}
