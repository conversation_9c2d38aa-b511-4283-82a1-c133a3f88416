# 微信小程序租房平台 API 接口文档

## 📋 项目概述

**项目名称**：微信小程序租房平台后端API
**技术栈**：uniapp + Vue2 + UniCloud + uni-admin
**云服务**：UniCloud 阿里云版
**数据库**：MongoDB (UniCloud)

## 🗄️ 数据库设计

### 1. house（房源集合）
```javascript
{
  _id: String,           // 房源ID
  title: String,         // 房源标题
  desc: String,          // 房源描述
  images: Array,         // 图片URL数组 ["url1", "url2"]
  location: {            // 位置信息
    address: String,     // 详细地址
    latitude: Number,    // 纬度
    longitude: Number,   // 经度
    district: String     // 区域
  },
  price: Number,         // 租金（元/月）
  type: String,          // 房型：1室1厅、2室1厅、合租等
  area: Number,          // 面积（平方米）
  floor: String,         // 楼层信息
  config: Array,         // 配置 ["空调", "洗衣机", "热水器"]
  contact: {             // 联系方式
    phone: String,       // 电话
    wechat: String       // 微信号
  },
  owner_id: String,      // 发布用户ID
  status: String,        // 状态：pending/approved/rejected
  reject_reason: String, // 驳回原因
  is_featured: Boolean,  // 是否推荐
  view_count: Number,    // 浏览次数
  created_at: Date,      // 创建时间
  updated_at: Date       // 更新时间
}
```

### 2. user（用户集合）
```javascript
{
  _id: String,           // 用户ID
  openid: String,        // 微信openid
  nickname: String,      // 微信昵称
  avatar: String,        // 头像URL
  phone: String,         // 手机号
  is_banned: Boolean,    // 是否被封禁
  ban_reason: String,    // 封禁原因
  created_at: Date,      // 注册时间
  last_login: Date       // 最后登录时间
}
```

### 3. admin（管理员集合）
```javascript
{
  _id: String,           // 管理员ID
  username: String,      // 管理员账号
  password: String,      // 密码（加密）
  role: String,          // 角色：super_admin/admin
  nickname: String,      // 昵称
  created_at: Date       // 创建时间
}
```

### 4. favorites（收藏集合）
```javascript
{
  _id: String,           // 收藏ID
  user_id: String,       // 用户ID
  house_id: String,      // 房源ID
  created_at: Date       // 收藏时间
}
```

### 5. reports（举报集合）
```javascript
{
  _id: String,           // 举报ID
  house_id: String,      // 房源ID
  user_id: String,       // 举报用户ID
  reason: String,        // 举报原因
  desc: String,          // 详细描述
  status: String,        // 处理状态：pending/handled
  created_at: Date       // 举报时间
}
```

## 🔌 云函数API接口规范

### 用户认证模块

#### 1. 微信登录
**云函数名**：`user-auth`
**方法**：`wxLogin`
**请求参数**：
```javascript
{
  code: String,          // 微信登录code
  userInfo: Object       // 微信用户信息
}
```
**返回数据**：
```javascript
{
  code: 0,               // 状态码 0成功
  message: "登录成功",
  data: {
    token: String,       // JWT token
    userInfo: Object,    // 用户信息
    isNewUser: Boolean   // 是否新用户
  }
}
```

#### 2. 获取用户信息
**云函数名**：`user-auth`
**方法**：`getUserInfo`
**请求参数**：
```javascript
{
  token: String          // 用户token
}
```

#### 3. 更新用户信息
**云函数名**：`user-auth`
**方法**：`updateUserInfo`
**请求参数**：
```javascript
{
  phone: String,         // 手机号
  nickname: String       // 昵称
}
```

### 房源管理模块

#### 1. 获取房源列表
**云函数名**：`house-manage`
**方法**：`getHouseList`
**请求参数**：
```javascript
{
  page: Number,          // 页码，默认1
  limit: Number,         // 每页数量，默认10
  keyword: String,       // 搜索关键词
  district: String,      // 区域筛选
  priceMin: Number,      // 最低价格
  priceMax: Number,      // 最高价格
  type: String,          // 房型筛选
  sortBy: String         // 排序：price_asc/price_desc/time_desc
}
```
**返回数据**：
```javascript
{
  code: 0,
  message: "获取成功",
  data: {
    list: Array,         // 房源列表
    total: Number,       // 总数
    page: Number,        // 当前页
    hasMore: Boolean     // 是否有更多
  }
}
```

#### 2. 获取房源详情
**云函数名**：`house-manage`
**方法**：`getHouseDetail`
**请求参数**：
```javascript
{
  houseId: String        // 房源ID
}
```

#### 3. 发布房源
**云函数名**：`house-manage`
**方法**：`publishHouse`
**请求参数**：
```javascript
{
  title: String,         // 房源标题
  desc: String,          // 房源描述
  images: Array,         // 图片URL数组
  location: Object,      // 位置信息
  price: Number,         // 租金
  type: String,          // 房型
  area: Number,          // 面积
  floor: String,         // 楼层
  config: Array,         // 配置
  contact: Object        // 联系方式
}
```

#### 4. 编辑房源
**云函数名**：`house-manage`
**方法**：`updateHouse`

#### 5. 删除房源
**云函数名**：`house-manage`
**方法**：`deleteHouse`

#### 6. 获取推荐房源
**云函数名**：`house-manage`
**方法**：`getFeaturedHouses`

### 收藏管理模块

#### 1. 添加收藏
**云函数名**：`favorite-manage`
**方法**：`addFavorite`
**请求参数**：
```javascript
{
  houseId: String        // 房源ID
}
```

#### 2. 取消收藏
**云函数名**：`favorite-manage`
**方法**：`removeFavorite`

#### 3. 获取收藏列表
**云函数名**：`favorite-manage`
**方法**：`getFavoriteList`

### 文件上传模块

#### 1. 获取上传凭证
**云函数名**：`file-upload`
**方法**：`getUploadToken`
**请求参数**：
```javascript
{
  fileType: String,      // 文件类型：image/video
  fileName: String       // 文件名
}
```

#### 2. 图片上传完成回调
**云函数名**：`file-upload`
**方法**：`uploadCallback`

### 管理后台API模块

#### 1. 管理员登录
**云函数名**：`admin-auth`
**方法**：`adminLogin`
**请求参数**：
```javascript
{
  username: String,      // 管理员账号
  password: String       // 密码
}
```

#### 2. 房源审核
**云函数名**：`admin-house`
**方法**：`auditHouse`
**请求参数**：
```javascript
{
  houseId: String,       // 房源ID
  status: String,        // approved/rejected
  reason: String         // 驳回原因（可选）
}
```

#### 3. 用户管理
**云函数名**：`admin-user`
**方法**：`banUser` / `unbanUser`

#### 4. 数据统计
**云函数名**：`admin-stats`
**方法**：`getDashboardData`

### 举报管理模块

#### 1. 举报房源
**云函数名**：`report-manage`
**方法**：`reportHouse`
**请求参数**：
```javascript
{
  houseId: String,       // 房源ID
  reason: String,        // 举报原因
  desc: String           // 详细描述
}
```

#### 2. 获取举报列表（管理后台）
**云函数名**：`admin-report`
**方法**：`getReportList`

### 管理后台用户管理模块

#### 1. 获取用户列表
**云函数名**：`admin-user`
**方法**：`getUserList`
**请求参数**：
```javascript
{
  page: Number,          // 页码
  limit: Number,         // 每页数量
  keyword: String,       // 搜索关键词
  isBanned: Boolean      // 是否被封禁
}
```

#### 2. 封禁/解封用户
**云函数名**：`admin-user`
**方法**：`banUser` / `unbanUser`
**请求参数**：
```javascript
{
  userId: String,        // 用户ID
  reason: String         // 封禁原因（封禁时必填）
}
```

### 数据统计模块

#### 1. 获取仪表板数据
**云函数名**：`admin-stats`
**方法**：`getDashboardData`
**返回数据**：
```javascript
{
  code: 0,
  message: "获取成功",
  data: {
    houseStats: {        // 房源统计
      total: Number,     // 总数
      pending: Number,   // 待审核
      approved: Number,  // 已通过
      rejected: Number   // 已驳回
    },
    userStats: {         // 用户统计
      total: Number,     // 总用户数
      todayNew: Number,  // 今日新增
      banned: Number     // 被封禁
    },
    todayStats: {        // 今日数据
      newHouses: Number, // 新增房源
      newUsers: Number,  // 新增用户
      reports: Number    // 新增举报
    }
  }
}
```

## 📝 接口调用示例

### 前端调用示例
```javascript
// 1. 微信登录
const loginResult = await uniCloud.callFunction({
  name: 'user-auth',
  data: {
    action: 'wxLogin',
    data: {
      code: 'wx_login_code',
      userInfo: {
        nickName: '用户昵称',
        avatarUrl: '头像URL'
      }
    }
  }
});

// 2. 获取房源列表
const houseListResult = await uniCloud.callFunction({
  name: 'house-manage',
  data: {
    action: 'getHouseList',
    data: {
      page: 1,
      limit: 10,
      keyword: '两室一厅',
      district: '朝阳区',
      priceMin: 1000,
      priceMax: 5000,
      sortBy: 'price_asc'
    }
  }
});

// 3. 发布房源
const publishResult = await uniCloud.callFunction({
  name: 'house-manage',
  data: {
    action: 'publishHouse',
    data: {
      token: 'user_token',
      title: '精装两室一厅',
      desc: '房源描述...',
      images: ['image1.jpg', 'image2.jpg'],
      location: {
        address: '北京市朝阳区xxx',
        latitude: 39.9042,
        longitude: 116.4074,
        district: '朝阳区'
      },
      price: 3000,
      type: '两室一厅',
      area: 80,
      floor: '10/20层',
      config: ['空调', '洗衣机', '热水器'],
      contact: {
        phone: '13800138000',
        wechat: 'wechat_id'
      }
    }
  }
});

// 4. 添加收藏
const favoriteResult = await uniCloud.callFunction({
  name: 'favorite-manage',
  data: {
    action: 'addFavorite',
    data: {
      token: 'user_token',
      houseId: 'house_id'
    }
  }
});
```

### 管理后台调用示例
```javascript
// 1. 管理员登录
const adminLoginResult = await uniCloud.callFunction({
  name: 'admin-auth',
  data: {
    action: 'adminLogin',
    data: {
      username: 'admin',
      password: 'password'
    }
  }
});

// 2. 审核房源
const auditResult = await uniCloud.callFunction({
  name: 'admin-house',
  data: {
    action: 'auditHouse',
    data: {
      token: 'admin_token',
      houseId: 'house_id',
      status: 'approved', // 或 'rejected'
      reason: '驳回原因' // 驳回时填写
    }
  }
});

// 3. 获取数据统计
const statsResult = await uniCloud.callFunction({
  name: 'admin-stats',
  data: {
    action: 'getDashboardData',
    data: {
      token: 'admin_token'
    }
  }
});
```

## 🔒 权限控制

### 数据库权限设置
- **house集合**：
  - 读取：所有人可读取已审核房源
  - 创建：登录用户可创建
  - 更新：房源所有者或管理员可更新
  - 删除：房源所有者或管理员可删除

- **user集合**：
  - 读取：用户本人或管理员可读取
  - 创建：登录用户可创建
  - 更新：用户本人或管理员可更新
  - 删除：仅管理员可删除

- **favorites集合**：
  - 读取：用户本人或管理员可读取
  - 创建：登录用户可创建
  - 删除：用户本人或管理员可删除

### 角色权限
- **游客**：查看房源列表、详情
- **普通用户**：发布房源、收藏、举报、管理个人信息
- **管理员**：审核房源、用户管理、查看举报
- **超级管理员**：所有权限 + 创建管理员

## 📊 状态码规范

- `0`：成功
- `1001`：参数错误
- `1002`：用户未登录
- `1003`：权限不足
- `1004`：资源不存在
- `1005`：操作失败
- `2001`：服务器错误

## 🚀 部署说明

### 云函数部署步骤
1. 在HBuilderX中右键云函数目录
2. 选择"上传并运行"
3. 等待部署完成

### 数据库Schema部署
1. 在HBuilderX中打开数据库Schema文件
2. 右键选择"上传Schema"
3. 确认数据库结构创建

### 初始化数据
```javascript
// 创建超级管理员账号
db.collection('admin').add({
  username: 'superadmin',
  password: 'hashed_password', // 使用admin-auth云函数的hashPassword方法加密
  nickname: '超级管理员',
  role: 'super_admin',
  created_at: new Date()
});
```
