// 测试云函数连接和功能
// 在HBuilderX中运行此文件来测试云函数

// 测试管理员登录
async function testAdminLogin() {
	try {
		console.log('测试管理员登录...');
		const result = await uniCloud.callFunction({
			name: 'admin-auth',
			data: {
				action: 'adminLogin',
				data: {
					username: 'admin',
					password: '123456'
				}
			}
		});
		
		console.log('登录结果:', result);
		
		if (result.result.code === 0) {
			console.log('✅ 管理员登录测试通过');
			return result.result.data.token;
		} else {
			console.log('❌ 管理员登录测试失败:', result.result.message);
			return null;
		}
	} catch (error) {
		console.log('❌ 管理员登录测试异常:', error);
		return null;
	}
}

// 测试房源统计
async function testHouseStats(token) {
	try {
		console.log('测试房源统计...');
		const result = await uniCloud.callFunction({
			name: 'admin-house',
			data: {
				action: 'getHouseStats',
				data: { token }
			}
		});
		
		console.log('房源统计结果:', result);
		
		if (result.result.code === 0) {
			console.log('✅ 房源统计测试通过');
		} else {
			console.log('❌ 房源统计测试失败:', result.result.message);
		}
	} catch (error) {
		console.log('❌ 房源统计测试异常:', error);
	}
}

// 测试用户统计
async function testUserStats(token) {
	try {
		console.log('测试用户统计...');
		const result = await uniCloud.callFunction({
			name: 'admin-user',
			data: {
				action: 'getUserStats',
				data: { token }
			}
		});
		
		console.log('用户统计结果:', result);
		
		if (result.result.code === 0) {
			console.log('✅ 用户统计测试通过');
		} else {
			console.log('❌ 用户统计测试失败:', result.result.message);
		}
	} catch (error) {
		console.log('❌ 用户统计测试异常:', error);
	}
}

// 主测试函数
async function runTests() {
	console.log('开始测试云函数...');
	console.log('='.repeat(50));
	
	// 测试登录
	const token = await testAdminLogin();
	
	if (token) {
		// 测试其他功能
		await testHouseStats(token);
		await testUserStats(token);
	}
	
	console.log('='.repeat(50));
	console.log('测试完成');
}

// 运行测试
runTests();
