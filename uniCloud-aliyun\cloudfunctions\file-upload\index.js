'use strict';

const uniID = require('uni-id-common');

/**
 * 文件上传云函数
 * 处理图片上传、获取上传凭证等功能
 */
exports.main = async (event, context) => {
  const { action, data } = event;
  const uniIdIns = uniID.createInstance({
    context
  });

  // 统一返回格式
  const response = {
    code: 0,
    message: '操作成功',
    data: null
  };

  try {
    switch (action) {
      case 'getUploadToken':
        return await getUploadToken(data, uniIdIns);
      case 'uploadCallback':
        return await uploadCallback(data, uniIdIns);
      case 'deleteFile':
        return await deleteFile(data, uniIdIns);
      default:
        response.code = 1001;
        response.message = '未知操作类型';
        return response;
    }
  } catch (error) {
    console.error('文件上传云函数错误:', error);
    response.code = 2001;
    response.message = '服务器错误';
    return response;
  }
};

/**
 * 获取上传凭证
 */
async function getUploadToken(data, uniIdIns) {
  try {
    // 验证token
    const checkTokenResult = await uniIdIns.checkToken(data.token);
    
    if (checkTokenResult.errCode !== 0) {
      return {
        code: 1002,
        message: '用户未登录或token已过期'
      };
    }

    const { fileType = 'image', fileName } = data;
    
    if (!fileName) {
      return {
        code: 1001,
        message: '缺少文件名'
      };
    }

    // 验证文件类型
    const allowedTypes = ['image', 'video'];
    if (!allowedTypes.includes(fileType)) {
      return {
        code: 1001,
        message: '不支持的文件类型'
      };
    }

    // 验证文件扩展名
    const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const videoExts = ['.mp4', '.avi', '.mov'];
    
    const fileExt = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    
    if (fileType === 'image' && !imageExts.includes(fileExt)) {
      return {
        code: 1001,
        message: '不支持的图片格式'
      };
    }
    
    if (fileType === 'video' && !videoExts.includes(fileExt)) {
      return {
        code: 1001,
        message: '不支持的视频格式'
      };
    }

    // 生成文件路径
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2);
    const filePath = `${fileType}s/${checkTokenResult.uid}/${timestamp}_${randomStr}${fileExt}`;

    // 获取云存储实例
    const cloudStorage = uniCloud.storage();
    
    // 生成上传凭证
    const uploadResult = await cloudStorage.getUploadFileOptions({
      cloudPath: filePath,
      cloudPathAsRealPath: true
    });

    return {
      code: 0,
      message: '获取上传凭证成功',
      data: {
        uploadOptions: uploadResult,
        filePath: filePath,
        fileType: fileType
      }
    };

  } catch (error) {
    console.error('获取上传凭证错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 上传完成回调
 */
async function uploadCallback(data, uniIdIns) {
  try {
    // 验证token
    const checkTokenResult = await uniIdIns.checkToken(data.token);
    
    if (checkTokenResult.errCode !== 0) {
      return {
        code: 1002,
        message: '用户未登录或token已过期'
      };
    }

    const { filePath, fileType } = data;
    
    if (!filePath) {
      return {
        code: 1001,
        message: '缺少文件路径'
      };
    }

    // 获取文件信息
    const cloudStorage = uniCloud.storage();
    const fileInfo = await cloudStorage.getFileInfo({
      fileList: [filePath]
    });

    if (fileInfo.fileList.length === 0) {
      return {
        code: 1004,
        message: '文件不存在'
      };
    }

    const file = fileInfo.fileList[0];

    // 如果是图片，可以进行压缩处理
    let processedUrl = file.download_url;
    
    if (fileType === 'image') {
      // 这里可以添加图片压缩逻辑
      // 例如：生成缩略图、压缩大小等
      processedUrl = file.download_url + '?x-oss-process=image/resize,w_800,h_600,m_lfit';
    }

    return {
      code: 0,
      message: '上传成功',
      data: {
        fileId: file.file_id,
        fileName: file.file_name,
        fileSize: file.file_size,
        downloadUrl: file.download_url,
        processedUrl: processedUrl,
        filePath: filePath
      }
    };

  } catch (error) {
    console.error('上传回调错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 删除文件
 */
async function deleteFile(data, uniIdIns) {
  try {
    // 验证token
    const checkTokenResult = await uniIdIns.checkToken(data.token);
    
    if (checkTokenResult.errCode !== 0) {
      return {
        code: 1002,
        message: '用户未登录或token已过期'
      };
    }

    const { filePath } = data;
    
    if (!filePath) {
      return {
        code: 1001,
        message: '缺少文件路径'
      };
    }

    // 验证文件是否属于当前用户
    if (!filePath.includes(checkTokenResult.uid)) {
      return {
        code: 1003,
        message: '无权限删除此文件'
      };
    }

    // 删除文件
    const cloudStorage = uniCloud.storage();
    await cloudStorage.deleteFile({
      fileList: [filePath]
    });

    return {
      code: 0,
      message: '删除成功'
    };

  } catch (error) {
    console.error('删除文件错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}
