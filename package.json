{"name": "rental-admin-backend", "version": "1.0.0", "description": "租房管理后台系统", "main": "main.js", "scripts": {"dev:app": "uni build --watch", "dev:h5": "uni build --watch --platform h5", "dev:mp-weixin": "uni build --watch --platform mp-weixin", "build:app": "uni build", "build:h5": "uni build --platform h5", "build:mp-weixin": "uni build --platform mp-weixin"}, "keywords": ["uniapp", "vue", "rental", "admin", "unicloud"], "author": "Developer", "license": "MIT", "dependencies": {"@dcloudio/uni-app": "^2.0.0", "@dcloudio/uni-h5": "^2.0.0", "@dcloudio/uni-mp-weixin": "^2.0.0"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^2.0.0", "@dcloudio/webpack-uni-mp-loader": "^2.0.0", "@dcloudio/webpack-uni-pages-loader": "^2.0.0"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}