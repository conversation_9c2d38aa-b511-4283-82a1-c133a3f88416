{"bsonType": "object", "description": "房源信息集合", "required": ["title", "desc", "location", "price", "type", "owner_id"], "properties": {"_id": {"description": "房源ID"}, "title": {"bsonType": "string", "description": "房源标题", "maxLength": 100, "minLength": 5, "title": "房源标题"}, "desc": {"bsonType": "string", "description": "房源描述", "maxLength": 1000, "title": "房源描述"}, "images": {"bsonType": "array", "description": "房源图片URL数组", "items": {"bsonType": "string", "format": "url"}, "maxItems": 9, "title": "房源图片"}, "location": {"bsonType": "object", "description": "位置信息", "required": ["address"], "properties": {"address": {"bsonType": "string", "description": "详细地址", "title": "详细地址"}, "latitude": {"bsonType": "number", "description": "纬度", "title": "纬度"}, "longitude": {"bsonType": "number", "description": "经度", "title": "经度"}, "district": {"bsonType": "string", "description": "区域", "title": "区域"}}, "title": "位置信息"}, "price": {"bsonType": "number", "description": "租金（元/月）", "minimum": 0, "title": "租金"}, "type": {"bsonType": "string", "description": "房型", "enum": ["一室一厅", "两室一厅", "三室一厅", "三室两厅", "四室两厅", "合租", "单间", "其他"], "title": "房型"}, "area": {"bsonType": "number", "description": "面积（平方米）", "minimum": 0, "title": "面积"}, "floor": {"bsonType": "string", "description": "楼层信息", "title": "楼层"}, "config": {"bsonType": "array", "description": "房源配置", "items": {"bsonType": "string", "enum": ["空调", "洗衣机", "热水器", "冰箱", "电视", "沙发", "床", "衣柜", "书桌", "宽带", "燃气", "暖气", "电梯", "停车位"]}, "title": "房源配置"}, "contact": {"bsonType": "object", "description": "联系方式", "properties": {"phone": {"bsonType": "string", "description": "联系电话", "pattern": "^1[3-9]\\d{9}$", "title": "联系电话"}, "wechat": {"bsonType": "string", "description": "微信号", "title": "微信号"}}, "title": "联系方式"}, "owner_id": {"bsonType": "string", "description": "发布用户ID", "foreignKey": "user._id", "title": "发布用户"}, "status": {"bsonType": "string", "description": "审核状态", "enum": ["pending", "approved", "rejected"], "defaultValue": "pending", "title": "审核状态"}, "reject_reason": {"bsonType": "string", "description": "驳回原因", "title": "驳回原因"}, "is_featured": {"bsonType": "bool", "description": "是否推荐", "defaultValue": false, "title": "是否推荐"}, "view_count": {"bsonType": "number", "description": "浏览次数", "defaultValue": 0, "minimum": 0, "title": "浏览次数"}, "created_at": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}, "title": "创建时间"}, "updated_at": {"bsonType": "timestamp", "description": "更新时间", "forceDefaultValue": {"$env": "now"}, "title": "更新时间"}}, "permission": {"read": true, "create": "auth.uid != null", "update": "auth.uid == doc.owner_id || 'admin' in auth.role", "delete": "auth.uid == doc.owner_id || 'admin' in auth.role"}, "index": [{"IndexName": "status_created", "MgoKeySchema": {"status": 1, "created_at": -1}}, {"IndexName": "owner_id", "MgoKeySchema": {"owner_id": 1}}, {"IndexName": "location_district", "MgoKeySchema": {"location.district": 1}}, {"IndexName": "price", "MgoKeySchema": {"price": 1}}]}