'use strict';

const uniID = require('uni-id-common');

/**
 * 用户认证云函数
 * 处理用户登录、注册、信息获取等功能
 */
exports.main = async (event, context) => {
  const { action, data } = event;
  const uniIdIns = uniID.createInstance({
    context
  });

  // 统一返回格式
  const response = {
    code: 0,
    message: '操作成功',
    data: null
  };

  try {
    switch (action) {
      case 'wxLogin':
        return await wxLogin(data, uniIdIns);
      case 'getUserInfo':
        return await getUserInfo(data, uniIdIns);
      case 'updateUserInfo':
        return await updateUserInfo(data, uniIdIns);
      case 'bindPhone':
        return await bindPhone(data, uniIdIns);
      default:
        response.code = 1001;
        response.message = '未知操作类型';
        return response;
    }
  } catch (error) {
    console.error('用户认证云函数错误:', error);
    response.code = 2001;
    response.message = '服务器错误';
    return response;
  }
};

/**
 * 微信登录
 */
async function wxLogin(data, uniIdIns) {
  const { code, userInfo } = data;
  
  if (!code) {
    return {
      code: 1001,
      message: '缺少微信登录code'
    };
  }

  try {
    // 使用uni-id进行微信登录
    const loginResult = await uniIdIns.loginByWeixin({
      code,
      platform: 'mp-weixin'
    });

    if (loginResult.errCode !== 0) {
      return {
        code: 1005,
        message: loginResult.errMsg || '微信登录失败'
      };
    }

    // 获取数据库实例
    const db = uniCloud.database();
    const userCollection = db.collection('user');

    // 检查用户是否已存在
    let userRecord = await userCollection.where({
      openid: loginResult.openid
    }).get();

    let isNewUser = false;
    let userId = loginResult.uid;

    // 如果是新用户，创建用户记录
    if (userRecord.data.length === 0) {
      isNewUser = true;
      const newUser = {
        openid: loginResult.openid,
        nickname: userInfo?.nickName || '微信用户',
        avatar: userInfo?.avatarUrl || '',
        created_at: new Date(),
        last_login: new Date(),
        is_banned: false
      };

      const createResult = await userCollection.add(newUser);
      userId = createResult.id;
    } else {
      // 更新最后登录时间
      await userCollection.doc(userRecord.data[0]._id).update({
        last_login: new Date(),
        nickname: userInfo?.nickName || userRecord.data[0].nickname,
        avatar: userInfo?.avatarUrl || userRecord.data[0].avatar
      });
      userId = userRecord.data[0]._id;
    }

    // 获取完整用户信息
    const userDetail = await userCollection.doc(userId).get();

    return {
      code: 0,
      message: '登录成功',
      data: {
        token: loginResult.token,
        userInfo: userDetail.data[0],
        isNewUser
      }
    };

  } catch (error) {
    console.error('微信登录错误:', error);
    return {
      code: 1005,
      message: '微信登录失败'
    };
  }
}

/**
 * 获取用户信息
 */
async function getUserInfo(data, uniIdIns) {
  try {
    // 验证token
    const checkTokenResult = await uniIdIns.checkToken(data.token);
    
    if (checkTokenResult.errCode !== 0) {
      return {
        code: 1002,
        message: '用户未登录或token已过期'
      };
    }

    const db = uniCloud.database();
    const userDetail = await db.collection('user').doc(checkTokenResult.uid).get();

    if (userDetail.data.length === 0) {
      return {
        code: 1004,
        message: '用户不存在'
      };
    }

    return {
      code: 0,
      message: '获取成功',
      data: userDetail.data[0]
    };

  } catch (error) {
    console.error('获取用户信息错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 更新用户信息
 */
async function updateUserInfo(data, uniIdIns) {
  try {
    // 验证token
    const checkTokenResult = await uniIdIns.checkToken(data.token);
    
    if (checkTokenResult.errCode !== 0) {
      return {
        code: 1002,
        message: '用户未登录或token已过期'
      };
    }

    const db = uniCloud.database();
    const updateData = {};

    // 只更新允许的字段
    if (data.nickname) updateData.nickname = data.nickname;
    if (data.phone) updateData.phone = data.phone;
    if (data.avatar) updateData.avatar = data.avatar;

    updateData.updated_at = new Date();

    await db.collection('user').doc(checkTokenResult.uid).update(updateData);

    return {
      code: 0,
      message: '更新成功'
    };

  } catch (error) {
    console.error('更新用户信息错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}

/**
 * 绑定手机号
 */
async function bindPhone(data, uniIdIns) {
  try {
    // 验证token
    const checkTokenResult = await uniIdIns.checkToken(data.token);
    
    if (checkTokenResult.errCode !== 0) {
      return {
        code: 1002,
        message: '用户未登录或token已过期'
      };
    }

    const { phone } = data;
    
    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
      return {
        code: 1001,
        message: '手机号格式不正确'
      };
    }

    const db = uniCloud.database();
    
    // 检查手机号是否已被使用
    const existUser = await db.collection('user').where({
      phone,
      _id: db.command.neq(checkTokenResult.uid)
    }).get();

    if (existUser.data.length > 0) {
      return {
        code: 1005,
        message: '该手机号已被其他用户绑定'
      };
    }

    // 更新用户手机号
    await db.collection('user').doc(checkTokenResult.uid).update({
      phone,
      updated_at: new Date()
    });

    return {
      code: 0,
      message: '绑定成功'
    };

  } catch (error) {
    console.error('绑定手机号错误:', error);
    return {
      code: 2001,
      message: '服务器错误'
    };
  }
}
